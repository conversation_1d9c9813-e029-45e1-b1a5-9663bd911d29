package config

import (
	"os"
)

// JiraConfig holds JIRA configuration from environment variables
type JiraConfig struct {
	BaseURL  string
	Username string
	APIToken string
}

// GetJiraConfig returns JIRA configuration from environment variables
func GetJiraConfig() *JiraConfig {
	return &JiraConfig{
		BaseURL:  getEnv("JIRA_BASE_URL", "https://your-company.atlassian.net"),
		Username: getEnv("JIRA_USERNAME", "<EMAIL>"),
		APIToken: getEnv("JIRA_API_TOKEN", "your-jira-api-token-here"),
	}
}

// IsConfigured checks if JIRA is properly configured
func (jc *JiraConfig) IsConfigured() bool {
	return jc.BaseURL != "https://your-company.atlassian.net" &&
		jc.Username != "<EMAIL>" &&
		jc.APIToken != "your-jira-api-token-here" &&
		jc.BaseURL != "" &&
		jc.Username != "" &&
		jc.APIToken != ""
}

// getEnv gets environment variable with fallback
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
