-- Add new status fields to releases table
-- These fields track the status of different MT phases

ALTER TABLE releases ADD COLUMN prd_signoff_status VARCHAR(20) DEFAULT 'Not Start';
ALTER TABLE releases ADD COLUMN branchoff_status VARCHAR(20) DEFAULT 'Not Start';
ALTER TABLE releases ADD COLUMN code_freeze_status VARCHAR(20) DEFAULT 'Not Start';

-- Add check constraints to ensure valid status values
ALTER TABLE releases ADD CONSTRAINT check_prd_signoff_status 
    CHECK (prd_signoff_status IN ('Not Start', 'NA', 'Ongoing', 'Done'));

ALTER TABLE releases ADD CONSTRAINT check_branchoff_status 
    CHECK (branchoff_status IN ('Not Start', 'NA', 'Ongoing', 'Done'));

ALTER TABLE releases ADD CONSTRAINT check_code_freeze_status 
    CHECK (code_freeze_status IN ('Not Start', 'NA', 'Ongoing', 'Done'));
