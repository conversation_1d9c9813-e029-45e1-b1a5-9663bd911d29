package controllers

import (
	"net/http"
	"strconv"

	"release-management-backend/config"
	"release-management-backend/models"
	"release-management-backend/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type JiraSimpleController struct {
	DB *gorm.DB
}

func NewJiraSimpleController(db *gorm.DB) *JiraSimpleController {
	return &JiraSimpleController{DB: db}
}

// GetJiraBaseURL returns the JIRA base URL for frontend link generation
func (jsc *JiraSimpleController) GetJiraBaseURL(c *gin.Context) {
	jiraConfig := config.GetJiraConfig()

	if !jiraConfig.IsConfigured() {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "JIRA is not configured. Please check environment variables.",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"base_url": jiraConfig.BaseURL,
	})
}

// TestJiraConnection tests the JIRA connection using environment configuration
func (jsc *JiraSimpleController) TestJiraConnection(c *gin.Context) {
	jiraConfig := config.GetJiraConfig()

	if !jiraConfig.IsConfigured() {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "JIRA is not configured. Please check environment variables.",
		})
		return
	}

	jiraService := services.NewJiraService()
	if err := jiraService.TestConnection(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Connection test failed: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "JIRA connection successful",
		"base_url": jiraConfig.BaseURL,
	})
}

// GetFeaturesWithJira gets features for a release with JIRA data
func (jsc *JiraSimpleController) GetFeaturesWithJira(c *gin.Context) {
	releaseID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	filter := c.Query("filter")

	// Get local features
	var features []models.FeatureSummary
	result := jsc.DB.Where("release_id = ?", releaseID).Find(&features)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// If filter is provided, sync with JIRA
	if filter != "" {
		jiraConfig := config.GetJiraConfig()
		if !jiraConfig.IsConfigured() {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "JIRA is not configured",
			})
			return
		}

		jiraService := services.NewJiraService()

		// Determine if filter is JQL or Filter ID
		var jiraResponse *services.JiraSearchResponse
		if isNumeric(filter) {
			jiraResponse, err = jiraService.SearchIssuesByFilter(filter)
		} else {
			jiraResponse, err = jiraService.SearchIssuesByJQL(filter)
		}

		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "JIRA search failed: " + err.Error()})
			return
		}

		// Sync JIRA data with local features
		features = jsc.syncJiraFeatures(uint(releaseID), jiraResponse.Issues, features)
	}

	c.JSON(http.StatusOK, features)
}

// GetCCBsWithJira gets CCBs for a release with JIRA data
func (jsc *JiraSimpleController) GetCCBsWithJira(c *gin.Context) {
	releaseID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	filter := c.Query("filter")

	// Get local CCBs
	var ccbs []models.CCBSummary
	result := jsc.DB.Where("release_id = ?", releaseID).Find(&ccbs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// If filter is provided, sync with JIRA
	if filter != "" {
		jiraConfig := config.GetJiraConfig()
		if !jiraConfig.IsConfigured() {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "JIRA is not configured",
			})
			return
		}

		jiraService := services.NewJiraService()

		// Determine if filter is JQL or Filter ID
		var jiraResponse *services.JiraSearchResponse
		if isNumeric(filter) {
			jiraResponse, err = jiraService.SearchIssuesByFilter(filter)
		} else {
			jiraResponse, err = jiraService.SearchIssuesByJQL(filter)
		}

		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "JIRA search failed: " + err.Error()})
			return
		}

		// Sync JIRA data with local CCBs
		ccbs = jsc.syncJiraCCBs(uint(releaseID), jiraResponse.Issues, ccbs)
	}

	c.JSON(http.StatusOK, ccbs)
}

// Helper function to check if string is numeric
func isNumeric(s string) bool {
	_, err := strconv.Atoi(s)
	return err == nil
}

// Helper function to sync JIRA issues with local features
func (jsc *JiraSimpleController) syncJiraFeatures(releaseID uint, jiraIssues []services.JiraIssue, localFeatures []models.FeatureSummary) []models.FeatureSummary {
	// Create a map of existing features by JIRA key
	featureMap := make(map[string]*models.FeatureSummary)
	for i := range localFeatures {
		if localFeatures[i].JiraKey != "" {
			featureMap[localFeatures[i].JiraKey] = &localFeatures[i]
		}
	}

	// Process JIRA issues - convert to new model structure
	// In the new architecture, we don't store JIRA data in DB, only custom fields
	var convertedFeatures []models.FeatureSummary
	for _, issue := range jiraIssues {
		// Convert JIRA issue to FeatureSummary using the service function
		feature := services.ConvertJiraIssueToFeature(&issue, releaseID, nil)

		// Check if we have custom fields for this JIRA ID in local storage
		if existingFeature, exists := featureMap[issue.Key]; exists {
			feature.CustomFields = existingFeature.CustomFields
		}

		convertedFeatures = append(convertedFeatures, *feature)
	}

	return convertedFeatures
}

// Helper function to sync JIRA issues with local CCBs
func (jsc *JiraSimpleController) syncJiraCCBs(releaseID uint, jiraIssues []services.JiraIssue, localCCBs []models.CCBSummary) []models.CCBSummary {
	// Create a map of existing CCBs by JIRA key for custom fields lookup
	ccbMap := make(map[string]*models.CCBSummary)
	for i := range localCCBs {
		if localCCBs[i].JiraKey != "" {
			ccbMap[localCCBs[i].JiraKey] = &localCCBs[i]
		}
	}

	// Process JIRA issues - convert to new model structure
	// In the new architecture, we don't store JIRA data in DB, only custom fields
	var convertedCCBs []models.CCBSummary
	for _, issue := range jiraIssues {
		// Convert JIRA issue to CCBSummary using the service function
		ccb := services.ConvertJiraIssueToCCB(&issue, releaseID, nil)

		// Check if we have custom fields for this JIRA ID in local storage
		if existingCCB, exists := ccbMap[issue.Key]; exists {
			ccb.CustomFields = existingCCB.CustomFields
		}

		convertedCCBs = append(convertedCCBs, *ccb)
	}

	return convertedCCBs
}
