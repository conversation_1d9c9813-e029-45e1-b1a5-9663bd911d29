package controllers

import (
	"net/http"
	"strconv"

	"release-management-backend/models"
	"release-management-backend/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type JiraController struct {
	DB *gorm.DB
}

func NewJiraController(db *gorm.DB) *JiraController {
	return &JiraController{DB: db}
}

// GetJiraConfigs gets all JIRA configurations
func (jc *JiraController) GetJiraConfigs(c *gin.Context) {
	var configs []models.JiraConfig
	result := jc.DB.Find(&configs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Don't expose API tokens in response
	for i := range configs {
		configs[i].APIToken = "***"
	}

	c.JSON(http.StatusOK, configs)
}

// CreateJiraConfig creates a new JIRA configuration
func (jc *JiraController) CreateJiraConfig(c *gin.Context) {
	var config models.JiraConfig
	if err := c.ShouldBindJ<PERSON>(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// TODO: Encrypt API token before storing
	// For now, store as-is (in production, implement proper encryption)

	result := jc.DB.Create(&config)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Don't expose API token in response
	config.APIToken = "***"
	c.JSON(http.StatusCreated, config)
}

// UpdateJiraConfig updates a JIRA configuration
func (jc *JiraController) UpdateJiraConfig(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var config models.JiraConfig
	if result := jc.DB.First(&config, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "JIRA config not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	var updateData models.JiraConfig
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Don't update API token if it's masked
	if updateData.APIToken != "***" {
		config.APIToken = updateData.APIToken
	}
	config.Name = updateData.Name
	config.BaseURL = updateData.BaseURL
	config.Username = updateData.Username
	config.IsActive = updateData.IsActive

	if result := jc.DB.Save(&config); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Don't expose API token in response
	config.APIToken = "***"
	c.JSON(http.StatusOK, config)
}

// DeleteJiraConfig deletes a JIRA configuration
func (jc *JiraController) DeleteJiraConfig(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	result := jc.DB.Delete(&models.JiraConfig{}, id)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "JIRA config not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "JIRA config deleted successfully"})
}

// TestJiraConnection tests a JIRA connection
func (jc *JiraController) TestJiraConnection(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var config models.JiraConfig
	if result := jc.DB.First(&config, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "JIRA config not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	jiraService := services.NewJiraService(&config)
	if err := jiraService.TestConnection(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Connection test failed: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Connection test successful"})
}

// GetFilterConfigs gets JIRA filter configurations for a release
func (jc *JiraController) GetFilterConfigs(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("releaseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	var configs []models.JiraFilterConfig
	result := jc.DB.Where("release_id = ?", releaseID).Find(&configs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, configs)
}

// CreateFilterConfig creates a new JIRA filter configuration
func (jc *JiraController) CreateFilterConfig(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("releaseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	var config models.JiraFilterConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	config.ReleaseID = uint(releaseID)
	result := jc.DB.Create(&config)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, config)
}

// UpdateFilterConfig updates a JIRA filter configuration
func (jc *JiraController) UpdateFilterConfig(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var config models.JiraFilterConfig
	if result := jc.DB.First(&config, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Filter config not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if result := jc.DB.Save(&config); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, config)
}

// DeleteFilterConfig deletes a JIRA filter configuration
func (jc *JiraController) DeleteFilterConfig(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	result := jc.DB.Delete(&models.JiraFilterConfig{}, id)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filter config not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Filter config deleted successfully"})
}

// GetFieldMappings gets JIRA field mappings
func (jc *JiraController) GetFieldMappings(c *gin.Context) {
	mappingType := c.Query("type") // "feature" or "ccb"
	
	var mappings []models.JiraFieldMapping
	query := jc.DB
	if mappingType != "" {
		query = query.Where("type = ?", mappingType)
	}
	
	result := query.Find(&mappings)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, mappings)
}

// CreateFieldMapping creates a new JIRA field mapping
func (jc *JiraController) CreateFieldMapping(c *gin.Context) {
	var mapping models.JiraFieldMapping
	if err := c.ShouldBindJSON(&mapping); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result := jc.DB.Create(&mapping)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, mapping)
}

// UpdateFieldMapping updates a JIRA field mapping
func (jc *JiraController) UpdateFieldMapping(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var mapping models.JiraFieldMapping
	if result := jc.DB.First(&mapping, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Field mapping not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if err := c.ShouldBindJSON(&mapping); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if result := jc.DB.Save(&mapping); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, mapping)
}

// DeleteFieldMapping deletes a JIRA field mapping
func (jc *JiraController) DeleteFieldMapping(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	result := jc.DB.Delete(&models.JiraFieldMapping{}, id)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Field mapping not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Field mapping deleted successfully"})
}
