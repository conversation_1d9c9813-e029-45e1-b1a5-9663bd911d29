package controllers

import (
	"net/http"
	"release-management-backend/models"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RiskController struct {
	DB *gorm.DB
}

func NewRiskController(db *gorm.DB) *RiskController {
	return &RiskController{DB: db}
}

// GetRisks - Get all risks for a release
func (rc *RiskController) GetRisks(c *gin.Context) {
	releaseID := c.Param("id")
	var risks []models.RiskPart

	result := rc.DB.Where("release_id = ?", releaseID).Find(&risks)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, risks)
}

// CreateRisk - Create a new risk
func (rc *RiskController) CreateRisk(c *gin.Context) {
	releaseID := c.<PERSON>m("id")
	var risk models.RiskPart

	if err := c.ShouldBind<PERSON>(&risk); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.<PERSON>rror()})
		return
	}

	// Convert releaseID to uint
	id, err := strconv.ParseUint(releaseID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}
	risk.ReleaseID = uint(id)

	result := rc.DB.Create(&risk)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, risk)
}

// UpdateRisk - Update a risk
func (rc *RiskController) UpdateRisk(c *gin.Context) {
	id := c.Param("id")
	var risk models.RiskPart

	// Find the risk
	result := rc.DB.First(&risk, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Risk not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Update the risk
	if err := c.ShouldBindJSON(&risk); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result = rc.DB.Save(&risk)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, risk)
}

// DeleteRisk - Delete a risk
func (rc *RiskController) DeleteRisk(c *gin.Context) {
	id := c.Param("id")
	var risk models.RiskPart

	result := rc.DB.First(&risk, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Risk not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	result = rc.DB.Delete(&risk)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Risk deleted successfully"})
}
