package controllers

import (
	"net/http"
	"strconv"

	"release-management-backend/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type FeatureController struct {
	DB *gorm.DB
}

func NewFeatureController(db *gorm.DB) *FeatureController {
	return &FeatureController{DB: db}
}

// GetFeatureStates returns all valid feature states
func (fc *FeatureController) GetFeatureStates(c *gin.Context) {
	states := models.ValidFeatureStates()
	c.JSON(http.StatusOK, gin.H{"states": states})
}

// GetFeatures gets all features for a release
func (fc *FeatureController) GetFeatures(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	var features []models.FeatureSummary
	result := fc.DB.Where("release_id = ?", releaseID).Find(&features)
	if result.Error != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, features)
}

// CreateFeature creates a new feature
func (fc *FeatureController) CreateFeature(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	var feature models.FeatureSummary
	if err := c.ShouldBindJSON(&feature); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	feature.ReleaseID = uint(releaseID)
	result := fc.DB.Create(&feature)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, feature)
}

// UpdateFeature updates an existing feature
func (fc *FeatureController) UpdateFeature(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var feature models.FeatureSummary
	if result := fc.DB.First(&feature, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Feature not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if err := c.ShouldBindJSON(&feature); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if result := fc.DB.Save(&feature); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, feature)
}

// DeleteFeature deletes a feature
func (fc *FeatureController) DeleteFeature(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	result := fc.DB.Delete(&models.FeatureSummary{}, id)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Feature not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Feature deleted successfully"})
}

// GetFeaturesWithJira gets features for a release, including JIRA data
func (fc *FeatureController) GetFeaturesWithJira(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	// Get local features
	var localFeatures []models.FeatureSummary
	result := fc.DB.Where("release_id = ?", releaseID).Find(&localFeatures)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Get JIRA filter configuration for features
	var filterConfig models.JiraFilterConfig
	filterResult := fc.DB.Where("release_id = ? AND type = ? AND is_active = ?", releaseID, "feature", true).First(&filterConfig)

	allFeatures := localFeatures

	if filterResult.Error == nil {
		// Get active JIRA configuration
		var jiraConfig models.JiraConfig
		jiraResult := fc.DB.Where("is_active = ?", true).First(&jiraConfig)

		if jiraResult.Error == nil {
			// TODO: Implement JIRA integration
			// jiraService := services.NewJiraService(&jiraConfig)
			// jiraFeatures, err := fc.getJiraFeatures(jiraService, &filterConfig, uint(releaseID))
			// if err == nil {
			//     allFeatures = append(allFeatures, jiraFeatures...)
			// }
		}
	}

	c.JSON(http.StatusOK, allFeatures)
}

// SyncJiraFeatures synchronizes features from JIRA
func (fc *FeatureController) SyncJiraFeatures(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	// Get JIRA filter configuration for features
	var filterConfig models.JiraFilterConfig
	result := fc.DB.Where("release_id = ? AND type = ? AND is_active = ?", releaseID, "feature", true).First(&filterConfig)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active JIRA filter configuration found for features"})
		return
	}

	// Get active JIRA configuration
	var jiraConfig models.JiraConfig
	jiraResult := fc.DB.Where("is_active = ?", true).First(&jiraConfig)
	if jiraResult.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active JIRA configuration found"})
		return
	}

	// TODO: Implement JIRA synchronization
	// jiraService := services.NewJiraService(&jiraConfig)
	// syncedCount, err := fc.syncJiraFeatures(jiraService, &filterConfig, uint(releaseID))
	// if err != nil {
	//     c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	//     return
	// }

	c.JSON(http.StatusOK, gin.H{"message": "JIRA features sync completed", "synced_count": 0})
}
