package controllers

import (
	"net/http"
	"release-management-backend/models"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CCBController struct {
	DB *gorm.DB
}

func NewCCBController(db *gorm.DB) *CCBController {
	return &CCBController{DB: db}
}

// GetCCBs - Get all CCBs for a release
func (cc *CCBController) GetCCBs(c *gin.Context) {
	releaseID := c.Param("id")
	var ccbs []models.CCBSummary

	result := cc.DB.Where("release_id = ?", releaseID).Find(&ccbs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, ccbs)
}

// CreateCCB - Create a new CCB
func (cc *CCBController) CreateCCB(c *gin.Context) {
	releaseID := c.<PERSON>m("id")
	var ccb models.CCBSummary

	if err := c.<PERSON>(&ccb); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert releaseID to uint
	id, err := strconv.ParseUint(releaseID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}
	ccb.ReleaseID = uint(id)

	result := cc.DB.Create(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, ccb)
}

// UpdateCCB - Update a CCB
func (cc *CCBController) UpdateCCB(c *gin.Context) {
	id := c.Param("id")
	var ccb models.CCBSummary

	// Find the CCB
	result := cc.DB.First(&ccb, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "CCB not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Update the CCB
	if err := c.ShouldBindJSON(&ccb); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result = cc.DB.Save(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, ccb)
}

// DeleteCCB - Delete a CCB
func (cc *CCBController) DeleteCCB(c *gin.Context) {
	id := c.Param("id")
	var ccb models.CCBSummary

	result := cc.DB.First(&ccb, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "CCB not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	result = cc.DB.Delete(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "CCB deleted successfully"})
}

// GetCCBsWithJira gets CCBs for a release, including JIRA data
func (cc *CCBController) GetCCBsWithJira(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	// Get local CCBs
	var localCCBs []models.CCBSummary
	result := cc.DB.Where("release_id = ?", releaseID).Find(&localCCBs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Get JIRA filter configuration for CCBs
	var filterConfig models.JiraFilterConfig
	filterResult := cc.DB.Where("release_id = ? AND type = ? AND is_active = ?", releaseID, "ccb", true).First(&filterConfig)

	allCCBs := localCCBs

	if filterResult.Error == nil {
		// Get active JIRA configuration
		var jiraConfig models.JiraConfig
		jiraResult := cc.DB.Where("is_active = ?", true).First(&jiraConfig)

		if jiraResult.Error == nil {
			// TODO: Implement JIRA integration
			// jiraService := services.NewJiraService(&jiraConfig)
			// jiraCCBs, err := cc.getJiraCCBs(jiraService, &filterConfig, uint(releaseID))
			// if err == nil {
			//     allCCBs = append(allCCBs, jiraCCBs...)
			// }
		}
	}

	c.JSON(http.StatusOK, allCCBs)
}

// SyncJiraCCBs synchronizes CCBs from JIRA
func (cc *CCBController) SyncJiraCCBs(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	// Get JIRA filter configuration for CCBs
	var filterConfig models.JiraFilterConfig
	result := cc.DB.Where("release_id = ? AND type = ? AND is_active = ?", releaseID, "ccb", true).First(&filterConfig)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active JIRA filter configuration found for CCBs"})
		return
	}

	// Get active JIRA configuration
	var jiraConfig models.JiraConfig
	jiraResult := cc.DB.Where("is_active = ?", true).First(&jiraConfig)
	if jiraResult.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active JIRA configuration found"})
		return
	}

	// TODO: Implement JIRA synchronization
	// jiraService := services.NewJiraService(&jiraConfig)
	// syncedCount, err := cc.syncJiraCCBs(jiraService, &filterConfig, uint(releaseID))
	// if err != nil {
	//     c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	//     return
	// }

	c.JSON(http.StatusOK, gin.H{"message": "JIRA CCBs sync completed", "synced_count": 0})
}
