package controllers

import (
	"net/http"
	"release-management-backend/models"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CCBController struct {
	DB *gorm.DB
}

func NewCCBController(db *gorm.DB) *CCBController {
	return &CCBController{DB: db}
}

// GetCCBs - Get all CCBs for a release
func (cc *CCBController) GetCCBs(c *gin.Context) {
	releaseID := c.Param("id")
	var ccbs []models.CCBSummary

	result := cc.DB.Where("release_id = ?", releaseID).Find(&ccbs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, ccbs)
}

// CreateCCB - Create a new CCB
func (cc *CCBController) CreateCCB(c *gin.Context) {
	releaseID := c.<PERSON>m("id")
	var ccb models.CCBSummary

	if err := c.<PERSON>(&ccb); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert releaseID to uint
	id, err := strconv.ParseUint(releaseID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}
	ccb.ReleaseID = uint(id)

	result := cc.DB.Create(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, ccb)
}

// UpdateCCB - Update custom fields for a JIRA CCB
// If custom_fields is empty, the record will be deleted
func (cc *CCBController) UpdateCCB(c *gin.Context) {
	jiraID := c.Param("id") // Now using JIRA ID as identifier
	if jiraID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JIRA ID"})
		return
	}

	var updateData struct {
		CustomFields string `json:"custom_fields"`
		ReleaseID    uint   `json:"release_id"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// If custom_fields is empty, delete the record
	if updateData.CustomFields == "" || updateData.CustomFields == "{}" {
		result := cc.DB.Where("jira_id = ?", jiraID).Delete(&models.CCBSummary{})
		if result.Error != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Custom fields cleared"})
		return
	}

	// Upsert the custom fields
	ccb := models.CCBSummary{
		JiraID:       jiraID,
		ReleaseID:    updateData.ReleaseID,
		CustomFields: updateData.CustomFields,
	}

	result := cc.DB.Save(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, ccb)
}

// DeleteCCB is not needed - custom fields are cleared via UpdateCCB with empty values

// GetCCBsWithJira gets CCBs for a release, including JIRA data
func (cc *CCBController) GetCCBsWithJira(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	// Get local CCBs
	var localCCBs []models.CCBSummary
	result := cc.DB.Where("release_id = ?", releaseID).Find(&localCCBs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Get JIRA filter configuration for CCBs
	var filterConfig models.JiraFilterConfig
	filterResult := cc.DB.Where("release_id = ? AND type = ? AND is_active = ?", releaseID, "ccb", true).First(&filterConfig)

	allCCBs := localCCBs

	if filterResult.Error == nil {
		// Get active JIRA configuration
		var jiraConfig models.JiraConfig
		jiraResult := cc.DB.Where("is_active = ?", true).First(&jiraConfig)

		if jiraResult.Error == nil {
			// TODO: Implement JIRA integration
			// jiraService := services.NewJiraService(&jiraConfig)
			// jiraCCBs, err := cc.getJiraCCBs(jiraService, &filterConfig, uint(releaseID))
			// if err == nil {
			//     allCCBs = append(allCCBs, jiraCCBs...)
			// }
		}
	}

	c.JSON(http.StatusOK, allCCBs)
}

// SyncJiraCCBs synchronizes CCBs from JIRA
func (cc *CCBController) SyncJiraCCBs(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	// Get JIRA filter configuration for CCBs
	var filterConfig models.JiraFilterConfig
	result := cc.DB.Where("release_id = ? AND type = ? AND is_active = ?", releaseID, "ccb", true).First(&filterConfig)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active JIRA filter configuration found for CCBs"})
		return
	}

	// Get active JIRA configuration
	var jiraConfig models.JiraConfig
	jiraResult := cc.DB.Where("is_active = ?", true).First(&jiraConfig)
	if jiraResult.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active JIRA configuration found"})
		return
	}

	// TODO: Implement JIRA synchronization
	// jiraService := services.NewJiraService(&jiraConfig)
	// syncedCount, err := cc.syncJiraCCBs(jiraService, &filterConfig, uint(releaseID))
	// if err != nil {
	//     c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	//     return
	// }

	c.JSON(http.StatusOK, gin.H{"message": "JIRA CCBs sync completed", "synced_count": 0})
}
