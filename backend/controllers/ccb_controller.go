package controllers

import (
	"net/http"
	"release-management-backend/models"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CCBController struct {
	DB *gorm.DB
}

func NewCCBController(db *gorm.DB) *CCBController {
	return &CCBController{DB: db}
}

// GetCCBs - Get all CCBs for a release
func (cc *CCBController) GetCCBs(c *gin.Context) {
	releaseID := c.Param("id")
	var ccbs []models.CCBSummary

	result := cc.DB.Where("release_id = ?", releaseID).Find(&ccbs)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, ccbs)
}

// CreateCCB - Create a new CCB
func (cc *CCBController) CreateCCB(c *gin.Context) {
	releaseID := c.<PERSON>m("id")
	var ccb models.CCBSummary

	if err := c.<PERSON>(&ccb); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert releaseID to uint
	id, err := strconv.ParseUint(releaseID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}
	ccb.ReleaseID = uint(id)

	result := cc.DB.Create(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, ccb)
}

// UpdateCCB - Update a CCB
func (cc *CCBController) UpdateCCB(c *gin.Context) {
	id := c.Param("id")
	var ccb models.CCBSummary

	// Find the CCB
	result := cc.DB.First(&ccb, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "CCB not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Update the CCB
	if err := c.ShouldBindJSON(&ccb); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result = cc.DB.Save(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, ccb)
}

// DeleteCCB - Delete a CCB
func (cc *CCBController) DeleteCCB(c *gin.Context) {
	id := c.Param("id")
	var ccb models.CCBSummary

	result := cc.DB.First(&ccb, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "CCB not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	result = cc.DB.Delete(&ccb)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "CCB deleted successfully"})
}
