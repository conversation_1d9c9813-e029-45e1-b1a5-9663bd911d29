package models

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// FeatureState constants for simplified JIRA-style workflow
const (
	FeatureStateBacklog    = "BACKLOG"
	FeatureStateNew        = "NEW"
	FeatureStateInProgress = "IN-PROGRESS"
	FeatureStateCancelled  = "CANCELLED"
	FeatureStateClosed     = "CLOSED"
	FeatureStateResolved   = "RESOLVED"
)

// Release status constants for MT phases
const (
	StatusNotStart = "Not Start"
	StatusNA       = "NA"
	StatusOngoing  = "Ongoing"
	StatusDone     = "Done"
)

// ValidFeatureStates returns all valid feature states
func ValidFeatureStates() []string {
	return []string{
		FeatureStateBacklog,
		FeatureStateNew,
		FeatureStateInProgress,
		FeatureStateCancelled,
		FeatureStateClosed,
		FeatureStateResolved,
	}
}

// ValidReleaseStatuses returns all valid release status values
func ValidReleaseStatuses() []string {
	return []string{
		StatusNotStart,
		StatusNA,
		StatusOngoing,
		StatusDone,
	}
}

// CustomDate handles date parsing for forms
type CustomDate struct {
	time.Time
}

func (cd *CustomDate) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	if s == "null" || s == "" {
		cd.Time = time.Time{}
		return nil
	}

	// Try different date formats
	formats := []string{
		"2006-01-02",
		"2006-01-02T15:04:05Z07:00",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, s); err == nil {
			cd.Time = t
			return nil
		}
	}

	return fmt.Errorf("cannot parse %q as date", s)
}

func (cd CustomDate) MarshalJSON() ([]byte, error) {
	if cd.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte("\"" + cd.Time.Format("2006-01-02") + "\""), nil
}

func (cd CustomDate) Value() (driver.Value, error) {
	if cd.Time.IsZero() {
		return nil, nil
	}
	return cd.Time, nil
}

func (cd *CustomDate) Scan(value interface{}) error {
	if value == nil {
		cd.Time = time.Time{}
		return nil
	}
	cd.Time = value.(time.Time)
	return nil
}

// Release model
type Release struct {
	ID   uint   `json:"id" gorm:"primaryKey"`
	Name string `json:"name" gorm:"not null"`

	// MT0 - PRD阶段
	PRDSignoff       *CustomDate `json:"prd_signoff" gorm:"type:date"`
	PRDLink          string      `json:"prd_link"`
	PRDSignoffStatus string      `json:"prd_signoff_status"` // Not Start, NA, Ongoing, Done

	// MT1 - 测试策略阶段
	TestStrategySignoff *CustomDate `json:"test_strategy_signoff" gorm:"type:date"`
	TestStrategyLink    string      `json:"test_strategy_link"`
	ReleaseBranchOff    *CustomDate `json:"release_branch_off" gorm:"type:date"`
	BranchoffStatus     string      `json:"branchoff_status"` // Not Start, NA, Ongoing, Done

	// MT2 - 代码冻结阶段
	ReleaseCodeFreeze *CustomDate `json:"release_code_freeze" gorm:"type:date"`
	CodeFreezeStatus  string      `json:"code_freeze_status"` // Not Start, NA, Ongoing, Done

	// MT3 - 发布阶段
	State            string `json:"state"`
	TestReport       string `json:"test_report"`
	RiskLink         string `json:"risk_link"` // 新增字段
	RiskState        string `json:"risk_state"`
	SoftwareDownload string `json:"software_download"` // 新增字段，重命名原softwares字段
	DocLink          string `json:"doc_link"`          // 新增字段，重命名原docs字段

	// MT4 - 总结阶段
	Lessons string `json:"lessons"`

	// 保留旧字段以向后兼容，标记为deprecated
	Risk      string `json:"risk,omitempty"`      // deprecated: 使用risk_link替代
	Softwares string `json:"softwares,omitempty"` // deprecated: 使用software_download替代
	Docs      string `json:"docs,omitempty"`      // deprecated: 使用doc_link替代

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	FeatureSummaries []FeatureSummary `json:"feature_summaries,omitempty"`
	CCBSummaries     []CCBSummary     `json:"ccb_summaries,omitempty"`
	RiskParts        []RiskPart       `json:"risk_parts,omitempty"`
	Qualities        []Quality        `json:"qualities,omitempty"`
}

// FeatureSummary model - simplified to store only custom attributes
// Main data comes from JIRA, this model only stores local customizations
type FeatureSummary struct {
	JiraKey   string `json:"jira_key" gorm:"primaryKey"`       // JIRA issue key as primary key
	ReleaseID uint   `json:"release_id" gorm:"not null;index"` // Release association

	// JIRA data fields (populated from API, not stored locally)
	JiraID      string `json:"jira_id" gorm:"-"`      // From JIRA API
	Name        string `json:"name" gorm:"-"`         // From JIRA API
	Description string `json:"description" gorm:"-"`  // From JIRA API
	Owner       string `json:"owner" gorm:"-"`        // From JIRA API
	State       string `json:"state" gorm:"-"`        // From JIRA API
	IsFromJira  bool   `json:"is_from_jira" gorm:"-"` // Always true for JIRA features

	// Local custom fields (stored in database)
	CustomFields string `json:"custom_fields" gorm:"type:text"` // JSON string for custom attributes

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// CCBSummary model - simplified to store only custom attributes
// Main data comes from JIRA, this model only stores local customizations
type CCBSummary struct {
	JiraKey   string `json:"jira_key" gorm:"primaryKey"`       // JIRA issue key as primary key
	ReleaseID uint   `json:"release_id" gorm:"not null;index"` // Release association

	// JIRA data fields (populated from API, not stored locally)
	JiraID      string `json:"jira_id" gorm:"-"`      // From JIRA API
	Name        string `json:"name" gorm:"-"`         // From JIRA API
	Creator     string `json:"creator" gorm:"-"`      // From JIRA API
	Description string `json:"description" gorm:"-"`  // From JIRA API
	State       string `json:"state" gorm:"-"`        // From JIRA API
	IsFromJira  bool   `json:"is_from_jira" gorm:"-"` // Always true for JIRA CCBs

	// Local custom fields (stored in database)
	CustomFields string `json:"custom_fields" gorm:"type:text"` // JSON string for custom attributes

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// RiskPart model
type RiskPart struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	ReleaseID        uint           `json:"release_id" gorm:"not null"`
	Team             string         `json:"team"`
	Type             string         `json:"type"`
	Description      string         `json:"description"`
	Probability      string         `json:"probability"`
	Impact           string         `json:"impact"`
	Status           string         `json:"status"`
	Migration        string         `json:"migration"`
	FallbackStrategy string         `json:"fallback_strategy"`
	Keeper           string         `json:"keeper"`
	Comments         string         `json:"comments"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// Quality model
type Quality struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	ReleaseID uint           `json:"release_id" gorm:"not null"`
	ImageURL  string         `json:"image_url"`
	Title     string         `json:"title"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// JiraConfig model - stores JIRA connection configuration
type JiraConfig struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	Name     string `json:"name" gorm:"not null"` // Configuration name for identification
	BaseURL  string `json:"base_url" gorm:"not null"`
	Username string `json:"username" gorm:"not null"`
	APIToken string `json:"api_token" gorm:"not null"` // Encrypted API token
	IsActive bool   `json:"is_active" gorm:"default:false"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// JiraFilterConfig model - stores JIRA filter configurations for features and CCBs
type JiraFilterConfig struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	ReleaseID uint   `json:"release_id" gorm:"not null"`
	Type      string `json:"type" gorm:"not null"` // "feature" or "ccb"
	FilterID  string `json:"filter_id"`            // JIRA filter ID
	FilterJQL string `json:"filter_jql"`           // JQL query string
	IsActive  bool   `json:"is_active" gorm:"default:true"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// JiraFieldMapping model - maps JIRA fields to local fields
type JiraFieldMapping struct {
	ID         uint   `json:"id" gorm:"primaryKey"`
	Type       string `json:"type" gorm:"not null"`        // "feature" or "ccb"
	JiraField  string `json:"jira_field" gorm:"not null"`  // JIRA field name/ID
	LocalField string `json:"local_field" gorm:"not null"` // Local model field name
	IsActive   bool   `json:"is_active" gorm:"default:true"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}
