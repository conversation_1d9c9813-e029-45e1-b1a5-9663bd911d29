package models

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// FeatureState constants for simplified JIRA-style workflow
const (
	FeatureStateBacklog    = "BACKLOG"
	FeatureStateNew        = "NEW"
	FeatureStateInProgress = "IN-PROGRESS"
	FeatureStateCancelled  = "CANCELLED"
	FeatureStateClosed     = "CLOSED"
	FeatureStateResolved   = "RESOLVED"
)

// Release status constants for MT phases
const (
	StatusNotStart = "Not Start"
	StatusNA       = "NA"
	StatusOngoing  = "Ongoing"
	StatusDone     = "Done"
)

// ValidFeatureStates returns all valid feature states
func ValidFeatureStates() []string {
	return []string{
		FeatureStateBacklog,
		FeatureStateNew,
		FeatureStateInProgress,
		FeatureStateCancelled,
		FeatureStateClosed,
		FeatureStateResolved,
	}
}

// ValidReleaseStatuses returns all valid release status values
func ValidReleaseStatuses() []string {
	return []string{
		StatusNotStart,
		StatusNA,
		StatusOngoing,
		StatusDone,
	}
}

// CustomDate handles date parsing for forms
type CustomDate struct {
	time.Time
}

func (cd *CustomDate) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	if s == "null" || s == "" {
		cd.Time = time.Time{}
		return nil
	}

	// Try different date formats
	formats := []string{
		"2006-01-02",
		"2006-01-02T15:04:05Z07:00",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, s); err == nil {
			cd.Time = t
			return nil
		}
	}

	return fmt.Errorf("cannot parse %q as date", s)
}

func (cd CustomDate) MarshalJSON() ([]byte, error) {
	if cd.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte("\"" + cd.Time.Format("2006-01-02") + "\""), nil
}

func (cd CustomDate) Value() (driver.Value, error) {
	if cd.Time.IsZero() {
		return nil, nil
	}
	return cd.Time, nil
}

func (cd *CustomDate) Scan(value interface{}) error {
	if value == nil {
		cd.Time = time.Time{}
		return nil
	}
	cd.Time = value.(time.Time)
	return nil
}

// Release model
type Release struct {
	ID   uint   `json:"id" gorm:"primaryKey"`
	Name string `json:"name" gorm:"not null"`

	// MT0 - PRD阶段
	PRDSignoff       *CustomDate `json:"prd_signoff" gorm:"type:date"`
	PRDLink          string      `json:"prd_link"`
	PRDSignoffStatus string      `json:"prd_signoff_status"` // Not Start, NA, Ongoing, Done

	// MT1 - 测试策略阶段
	TestStrategySignoff *CustomDate `json:"test_strategy_signoff" gorm:"type:date"`
	TestStrategyLink    string      `json:"test_strategy_link"`
	ReleaseBranchOff    *CustomDate `json:"release_branch_off" gorm:"type:date"`
	BranchoffStatus     string      `json:"branchoff_status"` // Not Start, NA, Ongoing, Done

	// MT2 - 代码冻结阶段
	ReleaseCodeFreeze *CustomDate `json:"release_code_freeze" gorm:"type:date"`
	CodeFreezeStatus  string      `json:"code_freeze_status"` // Not Start, NA, Ongoing, Done

	// MT3 - 发布阶段
	State            string `json:"state"`
	TestReport       string `json:"test_report"`
	RiskLink         string `json:"risk_link"` // 新增字段
	RiskState        string `json:"risk_state"`
	SoftwareDownload string `json:"software_download"` // 新增字段，重命名原softwares字段
	DocLink          string `json:"doc_link"`          // 新增字段，重命名原docs字段

	// MT4 - 总结阶段
	Lessons string `json:"lessons"`

	// 保留旧字段以向后兼容，标记为deprecated
	Risk      string `json:"risk,omitempty"`      // deprecated: 使用risk_link替代
	Softwares string `json:"softwares,omitempty"` // deprecated: 使用software_download替代
	Docs      string `json:"docs,omitempty"`      // deprecated: 使用doc_link替代

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	FeatureSummaries []FeatureSummary `json:"feature_summaries,omitempty"`
	CCBSummaries     []CCBSummary     `json:"ccb_summaries,omitempty"`
	RiskParts        []RiskPart       `json:"risk_parts,omitempty"`
	Qualities        []Quality        `json:"qualities,omitempty"`
}

// FeatureSummary model - simplified to store only custom attributes
// Main data comes from JIRA, this model only stores local customizations
type FeatureSummary struct {
	JiraID    string `json:"jira_id" gorm:"primaryKey"`        // JIRA issue ID as primary key
	ReleaseID uint   `json:"release_id" gorm:"not null;index"` // Release association

	// JIRA data fields (populated from API, not stored locally)
	JiraKey     string `json:"jira_key" gorm:"-"`    // From JIRA API (e.g., "PROJ-123")
	Summary     string `json:"summary" gorm:"-"`     // From JIRA API
	Description string `json:"description" gorm:"-"` // From JIRA API

	// Details section from JIRA
	Type            string `json:"type" gorm:"-"`              // Issue type
	Priority        string `json:"priority" gorm:"-"`          // Priority
	Status          string `json:"status" gorm:"-"`            // Status
	Resolution      string `json:"resolution" gorm:"-"`        // Resolution
	FixVersions     string `json:"fix_versions" gorm:"-"`      // Fix versions
	AffectsVersions string `json:"affects_versions" gorm:"-"`  // Affects versions
	Components      string `json:"components" gorm:"-"`        // Components
	Labels          string `json:"labels" gorm:"-"`            // Labels
	Template        string `json:"template" gorm:"-"`          // Template
	SWDesignSpec    string `json:"sw_design_spec" gorm:"-"`    // SW Design spec (optional)
	FeatureTestSpec string `json:"feature_test_spec" gorm:"-"` // Feature test spec (optional)

	// People section from JIRA
	Assignee string `json:"assignee" gorm:"-"` // Assignee
	Reporter string `json:"reporter" gorm:"-"` // Reporter
	Watchers string `json:"watchers" gorm:"-"` // Watchers

	// Dates section from JIRA
	Created           string `json:"created" gorm:"-"`             // Created date
	Updated           string `json:"updated" gorm:"-"`             // Updated date
	Resolved          string `json:"resolved" gorm:"-"`            // Resolved date
	DesignSpecSignoff string `json:"design_spec_signoff" gorm:"-"` // Design spec signoff date

	IsFromJira bool `json:"is_from_jira" gorm:"-"` // Always true for JIRA features

	// Local custom fields (stored in database)
	CustomFields string `json:"custom_fields" gorm:"type:text"` // JSON string for custom attributes

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// CCBSummary model - simplified to store only custom attributes
// Main data comes from JIRA, this model only stores local customizations
type CCBSummary struct {
	JiraID    string `json:"jira_id" gorm:"primaryKey"`        // JIRA issue ID as primary key
	ReleaseID uint   `json:"release_id" gorm:"not null;index"` // Release association

	// JIRA data fields (populated from API, not stored locally)
	JiraKey     string `json:"jira_key" gorm:"-"`    // From JIRA API (e.g., "PROJ-123")
	Summary     string `json:"summary" gorm:"-"`     // From JIRA API
	Description string `json:"description" gorm:"-"` // From JIRA API

	// Details section from JIRA
	Type            string `json:"type" gorm:"-"`             // Issue type
	Priority        string `json:"priority" gorm:"-"`         // Priority
	Status          string `json:"status" gorm:"-"`           // Status
	Resolution      string `json:"resolution" gorm:"-"`       // Resolution
	FixVersions     string `json:"fix_versions" gorm:"-"`     // Fix versions
	AffectsVersions string `json:"affects_versions" gorm:"-"` // Affects versions
	Components      string `json:"components" gorm:"-"`       // Components
	Labels          string `json:"labels" gorm:"-"`           // Labels

	// People section from JIRA
	Assignee string `json:"assignee" gorm:"-"` // Assignee
	Reporter string `json:"reporter" gorm:"-"` // Reporter
	Watchers string `json:"watchers" gorm:"-"` // Watchers

	// Dates section from JIRA
	Created string `json:"created" gorm:"-"` // Created date
	Updated string `json:"updated" gorm:"-"` // Updated date

	IsFromJira bool `json:"is_from_jira" gorm:"-"` // Always true for JIRA CCBs

	// Local custom fields (stored in database)
	CustomFields string `json:"custom_fields" gorm:"type:text"` // JSON string for custom attributes

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// RiskPart model
type RiskPart struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	ReleaseID        uint           `json:"release_id" gorm:"not null"`
	Team             string         `json:"team"`
	Type             string         `json:"type"`
	Description      string         `json:"description"`
	Probability      string         `json:"probability"`
	Impact           string         `json:"impact"`
	Status           string         `json:"status"`
	Migration        string         `json:"migration"`
	FallbackStrategy string         `json:"fallback_strategy"`
	Keeper           string         `json:"keeper"`
	Comments         string         `json:"comments"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// Quality model
type Quality struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	ReleaseID uint           `json:"release_id" gorm:"not null"`
	ImageURL  string         `json:"image_url"`
	Title     string         `json:"title"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}
