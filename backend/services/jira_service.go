package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"release-management-backend/config"
	"release-management-backend/models"
)

// JiraService handles JIRA API interactions
type JiraService struct {
	config *config.JiraConfig
	client *http.Client
}

// NewJiraService creates a new JIRA service instance
func NewJiraService() *JiraService {
	return &JiraService{
		config: config.GetJiraConfig(),
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// JiraIssue represents a JIRA issue response
type JiraIssue struct {
	ID     string `json:"id"`
	Key    string `json:"key"`
	Fields struct {
		Summary     string `json:"summary"`
		Description string `json:"description"`

		// Issue details
		IssueType struct {
			Name string `json:"name"`
		} `json:"issuetype"`
		Priority struct {
			Name string `json:"name"`
		} `json:"priority"`
		Status struct {
			Name string `json:"name"`
		} `json:"status"`
		Resolution struct {
			Name string `json:"name"`
		} `json:"resolution"`
		FixVersions []struct {
			Name string `json:"name"`
		} `json:"fixVersions"`
		Versions []struct {
			Name string `json:"name"`
		} `json:"versions"`
		Components []struct {
			Name string `json:"name"`
		} `json:"components"`
		Labels []string `json:"labels"`

		// People
		Assignee struct {
			DisplayName string `json:"displayName"`
		} `json:"assignee"`
		Reporter struct {
			DisplayName string `json:"displayName"`
		} `json:"reporter"`
		Watches struct {
			WatchCount int `json:"watchCount"`
		} `json:"watches"`

		// Dates
		Created  string `json:"created"`
		Updated  string `json:"updated"`
		Resolved string `json:"resolutiondate"`

		// Custom fields (these may vary by JIRA instance)
		CustomFields map[string]interface{} `json:"-"` // Will be populated dynamically

		// Specific custom fields we're interested in
		SWDesignSpec      string `json:"customfield_10001"` // Example field ID
		FeatureTestSpec   string `json:"customfield_10002"` // Example field ID
		DesignSpecSignoff string `json:"customfield_10003"` // Example field ID
		Template          string `json:"customfield_10004"` // Example field ID
	} `json:"fields"`
}

// JiraSearchResponse represents JIRA search API response
type JiraSearchResponse struct {
	Issues     []JiraIssue `json:"issues"`
	Total      int         `json:"total"`
	StartAt    int         `json:"startAt"`
	MaxResults int         `json:"maxResults"`
}

// TestConnection tests the JIRA connection
func (js *JiraService) TestConnection() error {
	req, err := http.NewRequest("GET", js.config.BaseURL+"/rest/api/2/myself", nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	js.setAuthHeader(req)

	resp, err := js.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to connect to JIRA: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read response body for more details
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("JIRA authentication failed: status %d, response: %s", resp.StatusCode, string(body))
	}

	return nil
}

// SearchIssuesByFilter searches JIRA issues using a filter ID
func (js *JiraService) SearchIssuesByFilter(filterID string) (*JiraSearchResponse, error) {
	url := fmt.Sprintf("%s/rest/api/2/search?jql=filter=%s", js.config.BaseURL, filterID)
	return js.searchIssues(url)
}

// SearchIssuesByJQL searches JIRA issues using JQL
func (js *JiraService) SearchIssuesByJQL(jql string) (*JiraSearchResponse, error) {
	encodedJQL := url.QueryEscape(jql)
	url := fmt.Sprintf("%s/rest/api/2/search?jql=%s", js.config.BaseURL, encodedJQL)
	return js.searchIssues(url)
}

// GetIssue gets a single JIRA issue by key
func (js *JiraService) GetIssue(issueKey string) (*JiraIssue, error) {
	url := fmt.Sprintf("%s/rest/api/2/issue/%s", js.config.BaseURL, issueKey)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	js.setAuthHeader(req)

	resp, err := js.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get issue: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get issue: status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var issue JiraIssue
	if err := json.Unmarshal(body, &issue); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &issue, nil
}

// searchIssues performs the actual search request
func (js *JiraService) searchIssues(url string) (*JiraSearchResponse, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	js.setAuthHeader(req)

	resp, err := js.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to search issues: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to search issues: status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var searchResponse JiraSearchResponse
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &searchResponse, nil
}

// setAuthHeader sets the authentication header for JIRA API requests
func (js *JiraService) setAuthHeader(req *http.Request) {
	// Use Bearer token authentication for JIRA Cloud
	req.Header.Set("Authorization", "Bearer "+js.config.APIToken)
	req.Header.Set("Content-Type", "application/json")
}

// Helper functions for converting JIRA data to strings
func extractVersionNames(versions []struct {
	Name string `json:"name"`
}) string {
	var names []string
	for _, v := range versions {
		names = append(names, v.Name)
	}
	return strings.Join(names, ", ")
}

func extractComponentNames(components []struct {
	Name string `json:"name"`
}) string {
	var names []string
	for _, c := range components {
		names = append(names, c.Name)
	}
	return strings.Join(names, ", ")
}

func joinLabels(labels []string) string {
	return strings.Join(labels, ", ")
}

// ConvertJiraIssueToFeature converts a JIRA issue to FeatureSummary
// Only populates JIRA data fields (not stored in DB), custom fields come from DB
func ConvertJiraIssueToFeature(issue *JiraIssue, releaseID uint, mappings []models.JiraFieldMapping) *models.FeatureSummary {
	feature := &models.FeatureSummary{
		JiraID:    issue.ID,
		ReleaseID: releaseID,

		// Basic JIRA data
		JiraKey:     issue.Key,
		Summary:     issue.Fields.Summary,
		Description: issue.Fields.Description,

		// Details section
		Type:            issue.Fields.IssueType.Name,
		Priority:        issue.Fields.Priority.Name,
		Status:          issue.Fields.Status.Name,
		Resolution:      issue.Fields.Resolution.Name,
		FixVersions:     extractVersionNames(issue.Fields.FixVersions),
		AffectsVersions: extractVersionNames(issue.Fields.Versions),
		Components:      extractComponentNames(issue.Fields.Components),
		Labels:          joinLabels(issue.Fields.Labels),
		Template:        issue.Fields.Template,
		SWDesignSpec:    issue.Fields.SWDesignSpec,
		FeatureTestSpec: issue.Fields.FeatureTestSpec,

		// People section
		Assignee: issue.Fields.Assignee.DisplayName,
		Reporter: issue.Fields.Reporter.DisplayName,
		Watchers: fmt.Sprintf("%d watchers", issue.Fields.Watches.WatchCount),

		// Dates section
		Created:           issue.Fields.Created,
		Updated:           issue.Fields.Updated,
		Resolved:          issue.Fields.Resolved,
		DesignSpecSignoff: issue.Fields.DesignSpecSignoff,

		IsFromJira: true,
		// CustomFields will be populated from DB if exists
		CustomFields: "",
	}

	return feature
}

// ConvertJiraIssueToCCB converts a JIRA issue to CCBSummary
// Only populates JIRA data fields (not stored in DB), custom fields come from DB
func ConvertJiraIssueToCCB(issue *JiraIssue, releaseID uint, mappings []models.JiraFieldMapping) *models.CCBSummary {
	ccb := &models.CCBSummary{
		JiraID:    issue.ID,
		ReleaseID: releaseID,

		// Basic JIRA data
		JiraKey:     issue.Key,
		Summary:     issue.Fields.Summary,
		Description: issue.Fields.Description,

		// Details section
		Type:            issue.Fields.IssueType.Name,
		Priority:        issue.Fields.Priority.Name,
		Status:          issue.Fields.Status.Name,
		Resolution:      issue.Fields.Resolution.Name,
		FixVersions:     extractVersionNames(issue.Fields.FixVersions),
		AffectsVersions: extractVersionNames(issue.Fields.Versions),
		Components:      extractComponentNames(issue.Fields.Components),
		Labels:          joinLabels(issue.Fields.Labels),

		// People section
		Assignee: issue.Fields.Assignee.DisplayName,
		Reporter: issue.Fields.Reporter.DisplayName,
		Watchers: fmt.Sprintf("%d watchers", issue.Fields.Watches.WatchCount),

		// Dates section
		Created:  issue.Fields.Created,
		Updated:  issue.Fields.Updated,
		Resolved: issue.Fields.Resolved,

		IsFromJira: true,
		// CustomFields will be populated from DB if exists
		CustomFields: "",
	}

	return ccb
}

// mapJiraStatusToFeatureState maps JIRA status to feature state
func mapJiraStatusToFeatureState(jiraStatus string) string {
	// This is a basic mapping - should be configurable
	statusMap := map[string]string{
		"To Do":       models.FeatureStateBacklog,
		"Backlog":     models.FeatureStateBacklog,
		"Open":        models.FeatureStateNew,
		"New":         models.FeatureStateNew,
		"In Progress": models.FeatureStateInProgress,
		"Done":        models.FeatureStateClosed,
		"Closed":      models.FeatureStateClosed,
		"Resolved":    models.FeatureStateResolved,
		"Cancelled":   models.FeatureStateCancelled,
	}

	if state, exists := statusMap[jiraStatus]; exists {
		return state
	}
	return models.FeatureStateNew // Default fallback
}
