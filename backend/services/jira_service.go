package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"release-management-backend/config"
	"release-management-backend/models"
)

// JiraService handles JIRA API interactions
type JiraService struct {
	config *config.JiraConfig
	client *http.Client
}

// NewJiraService creates a new JIRA service instance
func NewJiraService() *JiraService {
	return &JiraService{
		config: config.GetJiraConfig(),
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// JiraIssue represents a JIRA issue response
type JiraIssue struct {
	ID     string `json:"id"`
	Key    string `json:"key"`
	Fields struct {
		Summary     string `json:"summary"`
		Description string `json:"description"`
		Status      struct {
			Name string `json:"name"`
		} `json:"status"`
		Assignee struct {
			DisplayName string `json:"displayName"`
		} `json:"assignee"`
		Created string `json:"created"`
		Updated string `json:"updated"`
		// Add more fields as needed based on field mappings
	} `json:"fields"`
}

// JiraSearchResponse represents JIRA search API response
type JiraSearchResponse struct {
	Issues     []JiraIssue `json:"issues"`
	Total      int         `json:"total"`
	StartAt    int         `json:"startAt"`
	MaxResults int         `json:"maxResults"`
}

// TestConnection tests the JIRA connection
func (js *JiraService) TestConnection() error {
	req, err := http.NewRequest("GET", js.config.BaseURL+"/rest/api/2/myself", nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	js.setAuthHeader(req)

	resp, err := js.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to connect to JIRA: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read response body for more details
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("JIRA authentication failed: status %d, response: %s", resp.StatusCode, string(body))
	}

	return nil
}

// SearchIssuesByFilter searches JIRA issues using a filter ID
func (js *JiraService) SearchIssuesByFilter(filterID string) (*JiraSearchResponse, error) {
	url := fmt.Sprintf("%s/rest/api/2/search?jql=filter=%s", js.config.BaseURL, filterID)
	return js.searchIssues(url)
}

// SearchIssuesByJQL searches JIRA issues using JQL
func (js *JiraService) SearchIssuesByJQL(jql string) (*JiraSearchResponse, error) {
	encodedJQL := url.QueryEscape(jql)
	url := fmt.Sprintf("%s/rest/api/2/search?jql=%s", js.config.BaseURL, encodedJQL)
	return js.searchIssues(url)
}

// GetIssue gets a single JIRA issue by key
func (js *JiraService) GetIssue(issueKey string) (*JiraIssue, error) {
	url := fmt.Sprintf("%s/rest/api/2/issue/%s", js.config.BaseURL, issueKey)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	js.setAuthHeader(req)

	resp, err := js.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get issue: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get issue: status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var issue JiraIssue
	if err := json.Unmarshal(body, &issue); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &issue, nil
}

// searchIssues performs the actual search request
func (js *JiraService) searchIssues(url string) (*JiraSearchResponse, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	js.setAuthHeader(req)

	resp, err := js.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to search issues: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to search issues: status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var searchResponse JiraSearchResponse
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &searchResponse, nil
}

// setAuthHeader sets the authentication header for JIRA API requests
func (js *JiraService) setAuthHeader(req *http.Request) {
	// Use Bearer token authentication for JIRA Cloud
	req.Header.Set("Authorization", "Bearer "+js.config.APIToken)
	req.Header.Set("Content-Type", "application/json")
}

// ConvertJiraIssueToFeature converts a JIRA issue to FeatureSummary
// Only populates JIRA data fields (not stored in DB), custom fields come from DB
func ConvertJiraIssueToFeature(issue *JiraIssue, releaseID uint, mappings []models.JiraFieldMapping) *models.FeatureSummary {
	feature := &models.FeatureSummary{
		JiraKey:   issue.Key,
		ReleaseID: releaseID,
		// JIRA data fields (not stored in DB)
		JiraID:      issue.ID,
		Name:        issue.Fields.Summary,
		Description: issue.Fields.Description,
		Owner:       issue.Fields.Assignee.DisplayName,
		State:       mapJiraStatusToFeatureState(issue.Fields.Status.Name),
		IsFromJira:  true,
		// CustomFields will be populated from DB if exists
		CustomFields: "",
	}

	return feature
}

// ConvertJiraIssueToCCB converts a JIRA issue to CCBSummary
// Only populates JIRA data fields (not stored in DB), custom fields come from DB
func ConvertJiraIssueToCCB(issue *JiraIssue, releaseID uint, mappings []models.JiraFieldMapping) *models.CCBSummary {
	ccb := &models.CCBSummary{
		JiraKey:   issue.Key,
		ReleaseID: releaseID,
		// JIRA data fields (not stored in DB)
		JiraID:      issue.ID,
		Name:        issue.Fields.Summary,
		Description: issue.Fields.Description,
		Creator:     issue.Fields.Assignee.DisplayName,
		State:       issue.Fields.Status.Name,
		IsFromJira:  true,
		// CustomFields will be populated from DB if exists
		CustomFields: "",
	}

	return ccb
}

// mapJiraStatusToFeatureState maps JIRA status to feature state
func mapJiraStatusToFeatureState(jiraStatus string) string {
	// This is a basic mapping - should be configurable
	statusMap := map[string]string{
		"To Do":       models.FeatureStateBacklog,
		"Backlog":     models.FeatureStateBacklog,
		"Open":        models.FeatureStateNew,
		"New":         models.FeatureStateNew,
		"In Progress": models.FeatureStateInProgress,
		"Done":        models.FeatureStateClosed,
		"Closed":      models.FeatureStateClosed,
		"Resolved":    models.FeatureStateResolved,
		"Cancelled":   models.FeatureStateCancelled,
	}

	if state, exists := statusMap[jiraStatus]; exists {
		return state
	}
	return models.FeatureStateNew // Default fallback
}
