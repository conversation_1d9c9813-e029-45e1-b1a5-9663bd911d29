{"name": "release-management-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext vue,js,jsx,cjs,mjs,ts,tsx,cts,mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "vuetify": "^3.4.6", "@mdi/font": "^7.3.67"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "eslint-config-prettier": "^9.0.0", "prettier": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "sass": "^1.69.5"}}