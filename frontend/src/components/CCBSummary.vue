<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <div class="d-flex align-center">
        <v-btn-toggle
          v-model="dataSource"
          color="primary"
          variant="outlined"
          mandatory
          class="mr-4"
        >
          <v-btn value="local" size="small">
            <v-icon>mdi-database</v-icon>
            Local Data
          </v-btn>
          <v-btn value="jira" size="small">
            <v-icon>mdi-jira</v-icon>
            JIRA Data
          </v-btn>
        </v-btn-toggle>

        <v-btn
          v-if="dataSource === 'jira'"
          color="primary"
          variant="outlined"
          size="small"
          @click="syncJiraData"
          :loading="syncing"
        >
          <v-icon>mdi-sync</v-icon>
          Sync JIRA
        </v-btn>
      </div>

      <v-btn color="primary" @click="openAddDialog" :disabled="dataSource === 'jira'">
        <v-icon>mdi-plus</v-icon>
        Add CCB
      </v-btn>
    </div>

    <!-- Error Alert -->
    <v-alert
      v-if="showError"
      type="error"
      dismissible
      @click:close="showError = false"
      class="mb-4"
    >
      {{ errorMessage }}
    </v-alert>

    <v-data-table
      :headers="headers"
      :items="currentCCBs"
      :loading="loading"
      class="grouped-table elevation-1"
    >
      <template v-slot:item.state="{ item }">
        <v-chip :color="getStateColor(item.state)" size="small" variant="flat">
          {{ item.state }}
        </v-chip>
      </template>

      <template v-slot:item.created="{ item }">
        {{ formatDate(item.created) }}
      </template>

      <template v-slot:item.signoff_date="{ item }">
        {{ formatDate(item.signoff_date) }}
      </template>

      <template v-slot:item.description="{ item }">
        <div style="max-width: 200px" class="text-truncate">
          {{ item.description }}
        </div>
      </template>

      <template v-slot:item.jira_key="{ item }">
        <a
          v-if="item.jira_key"
          :href="`${jiraBaseUrl}/browse/${item.jira_key}`"
          target="_blank"
          class="text-decoration-none"
        >
          {{ item.jira_key }}
        </a>
        <span v-else>-</span>
      </template>

      <template v-slot:item.is_from_jira="{ item }">
        <v-chip
          :color="item.is_from_jira ? 'blue' : 'grey'"
          size="small"
          variant="flat"
          class="text-white"
        >
          {{ item.is_from_jira ? 'JIRA' : 'Local' }}
        </v-chip>
      </template>

      <template v-slot:item.actions="{ item }">
        <v-btn
          icon
          size="small"
          variant="text"
          @click.stop="openEditDialog(item)"
          :disabled="item.is_from_jira"
        >
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn
          icon
          size="small"
          variant="text"
          color="error"
          @click.stop="deleteCCB(item)"
          :disabled="item.is_from_jira"
        >
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- Add/Edit Dialog -->
    <CCBDialog v-model="dialogOpen" :ccb="selectedCCB" :loading="dialogLoading" @save="saveCCB" />

    <!-- Confirm Delete Dialog -->
    <v-dialog v-model="deleteDialogOpen" max-width="400px">
      <v-card>
        <v-card-title class="text-h5">Confirm Delete</v-card-title>
        <v-card-text>Are you sure you want to delete this CCB?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialogOpen = false">Cancel</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleteLoading">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import CCBDialog from './CCBDialog.vue'
import { jiraService } from '@/services/jiraService'

const props = defineProps({
  releaseId: Number,
  ccbSummaries: Array,
})

const emit = defineEmits(['refresh'])
const dataSource = ref('local')
const jiraCCBs = ref([])
const syncing = ref(false)
const jiraBaseUrl = ref('https://your-company.atlassian.net')
const errorMessage = ref('')
const showError = ref(false)

const loading = ref(false)
const dialogOpen = ref(false)
const dialogLoading = ref(false)
const deleteDialogOpen = ref(false)
const deleteLoading = ref(false)
const selectedCCB = ref(null)
const ccbToDelete = ref(null)

// Computed property to get the current CCBs based on data source
const currentCCBs = computed(() => {
  return dataSource.value === 'jira' ? jiraCCBs.value : props.ccbSummaries
})

// Watch for data source changes to load JIRA data when needed
watch(dataSource, async (newSource) => {
  if (newSource === 'jira' && jiraCCBs.value.length === 0) {
    await loadJiraCCBs()
  }
})

async function loadJiraCCBs() {
  try {
    syncing.value = true
    showError.value = false
    jiraCCBs.value = await jiraService.getCCBsWithJira(props.releaseId)
  } catch (error) {
    console.error('Failed to load JIRA CCBs:', error)
    errorMessage.value = 'Failed to load JIRA CCBs. Please check your JIRA configuration.'
    showError.value = true
    // Fall back to local data
    dataSource.value = 'local'
  } finally {
    syncing.value = false
  }
}

async function syncJiraData() {
  try {
    syncing.value = true
    showError.value = false
    await jiraService.syncJiraCCBs(props.releaseId)
    await loadJiraCCBs()
  } catch (error) {
    console.error('Failed to sync JIRA CCBs:', error)
    errorMessage.value =
      'Failed to sync JIRA CCBs. Please check your JIRA configuration and try again.'
    showError.value = true
  } finally {
    syncing.value = false
  }
}

// Load JIRA base URL on component mount
async function loadJiraBaseUrl() {
  try {
    jiraBaseUrl.value = await jiraService.getJiraBaseUrl()
  } catch (error) {
    console.error('Failed to load JIRA base URL:', error)
  }
}

// Dynamic headers based on data source
const headers = computed(() => {
  const baseHeaders = [
    { title: 'Name', key: 'name', sortable: true },
    { title: 'Creator', key: 'creator', sortable: true },
    { title: 'Description', key: 'description', sortable: false },
    { title: 'State', key: 'state', sortable: true },
    { title: 'Created', key: 'created', sortable: true },
    { title: 'Signoff Date', key: 'signoff_date', sortable: true },
  ]

  if (dataSource.value === 'jira') {
    // Add JIRA-specific columns
    baseHeaders.splice(1, 0, { title: 'JIRA Key', key: 'jira_key', sortable: true })
    baseHeaders.push({ title: 'Source', key: 'is_from_jira', sortable: true })
  }

  baseHeaders.push({ title: 'Actions', key: 'actions', sortable: false, width: '120' })
  return baseHeaders
})

function getStateColor(state) {
  const colors = {
    Accept: 'green',
    Reject: 'red',
  }
  return colors[state] || 'grey'
}

function formatDate(dateString) {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString()
}

function openAddDialog() {
  selectedCCB.value = null
  dialogOpen.value = true
}

function openEditDialog(ccb) {
  selectedCCB.value = { ...ccb }
  dialogOpen.value = true
}

async function saveCCB(ccbData) {
  dialogLoading.value = true
  try {
    if (selectedCCB.value?.id) {
      // Update existing CCB
      await updateCCB(selectedCCB.value.id, ccbData)
    } else {
      // Create new CCB
      await createCCB(ccbData)
    }
    dialogOpen.value = false
    emit('refresh')
  } catch (error) {
    console.error('Error saving CCB:', error)
    // TODO: Show error message to user
  } finally {
    dialogLoading.value = false
  }
}

async function createCCB(ccbData) {
  const response = await fetch(
    `${import.meta.env.VITE_API_BASE_URL}/api/releases/${props.releaseId}/ccbs`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ccbData),
    }
  )

  if (!response.ok) {
    throw new Error('Failed to create CCB')
  }

  return response.json()
}

async function updateCCB(ccbId, ccbData) {
  const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/ccbs/${ccbId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(ccbData),
  })

  if (!response.ok) {
    throw new Error('Failed to update CCB')
  }

  return response.json()
}

function deleteCCB(ccb) {
  ccbToDelete.value = ccb
  deleteDialogOpen.value = true
}

async function confirmDelete() {
  deleteLoading.value = true
  try {
    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/ccbs/${ccbToDelete.value.id}`,
      {
        method: 'DELETE',
      }
    )

    if (!response.ok) {
      throw new Error('Failed to delete CCB')
    }

    deleteDialogOpen.value = false
    emit('refresh')
  } catch (error) {
    console.error('Error deleting CCB:', error)
    // TODO: Show error message to user
  } finally {
    deleteLoading.value = false
  }
}

// Load JIRA base URL on component mount
onMounted(() => {
  loadJiraBaseUrl()
})
</script>

<style scoped>
/* 统一表格样式 */
.grouped-table :deep(.v-data-table__th) {
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
  color: #495057 !important;
}

.grouped-table :deep(.v-data-table__td) {
  padding: 8px 12px !important;
}

.v-data-table tbody tr {
  cursor: pointer;
}

.v-data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
