<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="700"
  >
    <v-card>
      <v-card-title>
        {{ filter?.id ? 'Edit JIRA Filter Configuration' : 'Create JIRA Filter Configuration' }}
      </v-card-title>

      <v-card-text>
        <v-form ref="form" v-model="valid">
          <v-select
            v-model="formData.type"
            label="Type"
            :items="typeOptions"
            :rules="[(v) => !!v || 'Type is required']"
            required
            variant="outlined"
            class="mb-3"
          ></v-select>

          <v-radio-group
            v-model="filterMethod"
            label="Filter Method"
            class="mb-3"
          >
            <v-radio
              label="Use Filter ID"
              value="filter_id"
            ></v-radio>
            <v-radio
              label="Use JQL Query"
              value="jql"
            ></v-radio>
          </v-radio-group>

          <v-text-field
            v-if="filterMethod === 'filter_id'"
            v-model="formData.filter_id"
            label="JIRA Filter ID"
            :rules="filterMethod === 'filter_id' ? [(v) => !!v || 'Filter ID is required'] : []"
            variant="outlined"
            class="mb-3"
            hint="The numeric ID of the JIRA filter (e.g., 12345)"
            persistent-hint
          ></v-text-field>

          <v-textarea
            v-if="filterMethod === 'jql'"
            v-model="formData.filter_jql"
            label="JQL Query"
            :rules="filterMethod === 'jql' ? [(v) => !!v || 'JQL query is required'] : []"
            variant="outlined"
            class="mb-3"
            rows="4"
            hint="JIRA Query Language (JQL) to filter issues"
            persistent-hint
            placeholder="project = 'YOUR_PROJECT' AND status IN ('To Do', 'In Progress')"
          ></v-textarea>

          <v-switch
            v-model="formData.is_active"
            label="Active"
            color="primary"
            class="mb-3"
          ></v-switch>

          <v-alert
            type="info"
            class="mb-3"
          >
            <div class="text-subtitle-2 mb-2">Examples:</div>
            <div class="text-body-2">
              <strong>Filter ID:</strong> Use the ID from JIRA filter URL (e.g., 12345)<br>
              <strong>JQL for Features:</strong> project = "MYPROJ" AND type = "Story" AND fixVersion = "v1.0"<br>
              <strong>JQL for CCBs:</strong> project = "MYPROJ" AND type = "Change Request" AND status != "Closed"
            </div>
          </v-alert>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        
        <v-btn
          color="grey"
          variant="text"
          @click="$emit('update:modelValue', false)"
        >
          Cancel
        </v-btn>
        
        <v-btn
          color="primary"
          variant="flat"
          @click="save"
          :loading="loading"
          :disabled="!valid"
        >
          {{ filter?.id ? 'Update' : 'Create' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed, defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  filter: Object,
  loading: Boolean,
})

const emit = defineEmits(['update:modelValue', 'save'])

const form = ref(null)
const valid = ref(false)
const filterMethod = ref('filter_id')

const formData = ref({
  type: '',
  filter_id: '',
  filter_jql: '',
  is_active: true,
})

const typeOptions = [
  { title: 'Feature', value: 'feature' },
  { title: 'CCB (Change Control Board)', value: 'ccb' },
]

watch(
  () => props.filter,
  (newFilter) => {
    if (newFilter) {
      formData.value = {
        type: newFilter.type || '',
        filter_id: newFilter.filter_id || '',
        filter_jql: newFilter.filter_jql || '',
        is_active: newFilter.is_active !== undefined ? newFilter.is_active : true,
      }
      
      // Determine filter method based on existing data
      if (newFilter.filter_id) {
        filterMethod.value = 'filter_id'
      } else if (newFilter.filter_jql) {
        filterMethod.value = 'jql'
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

// Clear the other field when switching methods
watch(filterMethod, (newMethod) => {
  if (newMethod === 'filter_id') {
    formData.value.filter_jql = ''
  } else {
    formData.value.filter_id = ''
  }
})

function resetForm() {
  formData.value = {
    type: '',
    filter_id: '',
    filter_jql: '',
    is_active: true,
  }
  filterMethod.value = 'filter_id'
}

async function save() {
  const isValid = await form.value?.validate()
  if (isValid?.valid) {
    const data = { ...formData.value }
    
    // Ensure only one filter method is set
    if (filterMethod.value === 'filter_id') {
      data.filter_jql = ''
    } else {
      data.filter_id = ''
    }
    
    emit('save', data)
  }
}
</script>

<style scoped>
.v-card-title {
  font-weight: 500;
}
</style>
