<template>
  <div class="jira-settings">
    <!-- JIRA Connection Configuration -->
    <v-card class="mb-6">
      <v-card-title class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-icon class="mr-2">mdi-connection</v-icon>
          JIRA Connection Configuration
        </div>
        <v-btn
          color="primary"
          variant="flat"
          @click="showConfigDialog = true"
        >
          <v-icon>mdi-plus</v-icon>
          Add Configuration
        </v-btn>
      </v-card-title>

      <v-card-text>
        <v-data-table
          :headers="configHeaders"
          :items="jiraConfigs"
          :loading="configLoading"
          class="elevation-0"
        >
          <template v-slot:item.is_active="{ item }">
            <v-chip
              :color="item.is_active ? 'success' : 'grey'"
              variant="flat"
              size="small"
            >
              {{ item.is_active ? 'Active' : 'Inactive' }}
            </v-chip>
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn
              icon="mdi-connection"
              size="small"
              variant="text"
              @click="testConnection(item)"
              :loading="testingConnection === item.id"
            ></v-btn>
            <v-btn
              icon="mdi-pencil"
              size="small"
              variant="text"
              @click="editConfig(item)"
            ></v-btn>
            <v-btn
              icon="mdi-delete"
              size="small"
              variant="text"
              color="error"
              @click="deleteConfig(item)"
            ></v-btn>
          </template>

          <template v-slot:no-data>
            <div class="text-center py-4">
              <v-icon size="48" color="grey">mdi-connection</v-icon>
              <div class="text-h6 mt-2">No JIRA configurations</div>
              <div class="text-body-2 text-grey">Add a JIRA configuration to get started</div>
            </div>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>

    <!-- JIRA Filter Configuration for Current Release -->
    <v-card v-if="releaseId" class="mb-6">
      <v-card-title class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-icon class="mr-2">mdi-filter</v-icon>
          JIRA Filters for Current Release
        </div>
        <v-btn
          color="primary"
          variant="flat"
          @click="showFilterDialog = true"
          :disabled="!hasActiveConfig"
        >
          <v-icon>mdi-plus</v-icon>
          Add Filter
        </v-btn>
      </v-card-title>

      <v-card-text>
        <v-alert
          v-if="!hasActiveConfig"
          type="warning"
          class="mb-4"
        >
          Please configure and activate a JIRA connection before setting up filters.
        </v-alert>

        <v-data-table
          :headers="filterHeaders"
          :items="filterConfigs"
          :loading="filterLoading"
          class="elevation-0"
        >
          <template v-slot:item.type="{ item }">
            <v-chip
              :color="item.type === 'feature' ? 'blue' : 'orange'"
              variant="flat"
              size="small"
            >
              {{ item.type === 'feature' ? 'Feature' : 'CCB' }}
            </v-chip>
          </template>

          <template v-slot:item.filter_method="{ item }">
            <div v-if="item.filter_id">
              <v-chip variant="outlined" size="small">Filter ID</v-chip>
              <div class="text-caption mt-1">{{ item.filter_id }}</div>
            </div>
            <div v-else-if="item.filter_jql">
              <v-chip variant="outlined" size="small">JQL</v-chip>
              <div class="text-caption mt-1" style="max-width: 200px; word-break: break-all;">
                {{ item.filter_jql }}
              </div>
            </div>
          </template>

          <template v-slot:item.is_active="{ item }">
            <v-chip
              :color="item.is_active ? 'success' : 'grey'"
              variant="flat"
              size="small"
            >
              {{ item.is_active ? 'Active' : 'Inactive' }}
            </v-chip>
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn
              icon="mdi-sync"
              size="small"
              variant="text"
              @click="syncData(item)"
              :loading="syncing === item.id"
            ></v-btn>
            <v-btn
              icon="mdi-pencil"
              size="small"
              variant="text"
              @click="editFilter(item)"
            ></v-btn>
            <v-btn
              icon="mdi-delete"
              size="small"
              variant="text"
              color="error"
              @click="deleteFilter(item)"
            ></v-btn>
          </template>

          <template v-slot:no-data>
            <div class="text-center py-4">
              <v-icon size="48" color="grey">mdi-filter</v-icon>
              <div class="text-h6 mt-2">No JIRA filters configured</div>
              <div class="text-body-2 text-grey">Add filters to sync data from JIRA</div>
            </div>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>

    <!-- Dialogs -->
    <JiraConfigDialog
      v-model="showConfigDialog"
      :config="selectedConfig"
      :loading="configSaving"
      @save="saveConfig"
    />

    <JiraFilterDialog
      v-model="showFilterDialog"
      :filter="selectedFilter"
      :loading="filterSaving"
      @save="saveFilter"
    />

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="3000"
    >
      {{ snackbar.message }}
    </v-snackbar>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, defineProps } from 'vue'
import { jiraService } from '@/services/jiraService'
import JiraConfigDialog from './JiraConfigDialog.vue'
import JiraFilterDialog from './JiraFilterDialog.vue'

const props = defineProps({
  releaseId: Number,
})

// Data
const jiraConfigs = ref([])
const filterConfigs = ref([])
const configLoading = ref(false)
const filterLoading = ref(false)
const configSaving = ref(false)
const filterSaving = ref(false)
const testingConnection = ref(null)
const syncing = ref(null)

// Dialog states
const showConfigDialog = ref(false)
const showFilterDialog = ref(false)
const selectedConfig = ref(null)
const selectedFilter = ref(null)

// Snackbar
const snackbar = ref({
  show: false,
  message: '',
  color: 'success',
})

// Table headers
const configHeaders = [
  { title: 'Name', key: 'name' },
  { title: 'Base URL', key: 'base_url' },
  { title: 'Username', key: 'username' },
  { title: 'Status', key: 'is_active' },
  { title: 'Actions', key: 'actions', sortable: false },
]

const filterHeaders = [
  { title: 'Type', key: 'type' },
  { title: 'Filter', key: 'filter_method' },
  { title: 'Status', key: 'is_active' },
  { title: 'Actions', key: 'actions', sortable: false },
]

// Computed
const hasActiveConfig = computed(() => {
  return jiraConfigs.value.some(config => config.is_active)
})

// Methods
onMounted(() => {
  loadJiraConfigs()
  if (props.releaseId) {
    loadFilterConfigs()
  }
})

async function loadJiraConfigs() {
  configLoading.value = true
  try {
    jiraConfigs.value = await jiraService.getJiraConfigs()
  } catch (error) {
    showSnackbar('Failed to load JIRA configurations', 'error')
  } finally {
    configLoading.value = false
  }
}

async function loadFilterConfigs() {
  if (!props.releaseId) return
  
  filterLoading.value = true
  try {
    filterConfigs.value = await jiraService.getFilterConfigs(props.releaseId)
  } catch (error) {
    showSnackbar('Failed to load filter configurations', 'error')
  } finally {
    filterLoading.value = false
  }
}

function editConfig(config) {
  selectedConfig.value = config
  showConfigDialog.value = true
}

function editFilter(filter) {
  selectedFilter.value = filter
  showFilterDialog.value = true
}

async function saveConfig(configData) {
  configSaving.value = true
  try {
    if (selectedConfig.value?.id) {
      await jiraService.updateJiraConfig(selectedConfig.value.id, configData)
      showSnackbar('JIRA configuration updated successfully')
    } else {
      await jiraService.createJiraConfig(configData)
      showSnackbar('JIRA configuration created successfully')
    }
    
    showConfigDialog.value = false
    selectedConfig.value = null
    await loadJiraConfigs()
  } catch (error) {
    showSnackbar(`Failed to save configuration: ${error.message}`, 'error')
  } finally {
    configSaving.value = false
  }
}

async function saveFilter(filterData) {
  filterSaving.value = true
  try {
    if (selectedFilter.value?.id) {
      await jiraService.updateFilterConfig(selectedFilter.value.id, filterData)
      showSnackbar('Filter configuration updated successfully')
    } else {
      await jiraService.createFilterConfig(props.releaseId, filterData)
      showSnackbar('Filter configuration created successfully')
    }
    
    showFilterDialog.value = false
    selectedFilter.value = null
    await loadFilterConfigs()
  } catch (error) {
    showSnackbar(`Failed to save filter: ${error.message}`, 'error')
  } finally {
    filterSaving.value = false
  }
}

async function testConnection(config) {
  testingConnection.value = config.id
  try {
    await jiraService.testJiraConnection(config.id)
    showSnackbar('Connection test successful')
  } catch (error) {
    showSnackbar(`Connection test failed: ${error.message}`, 'error')
  } finally {
    testingConnection.value = null
  }
}

async function deleteConfig(config) {
  if (confirm(`Are you sure you want to delete the configuration "${config.name}"?`)) {
    try {
      await jiraService.deleteJiraConfig(config.id)
      showSnackbar('Configuration deleted successfully')
      await loadJiraConfigs()
    } catch (error) {
      showSnackbar(`Failed to delete configuration: ${error.message}`, 'error')
    }
  }
}

async function deleteFilter(filter) {
  if (confirm('Are you sure you want to delete this filter configuration?')) {
    try {
      await jiraService.deleteFilterConfig(filter.id)
      showSnackbar('Filter deleted successfully')
      await loadFilterConfigs()
    } catch (error) {
      showSnackbar(`Failed to delete filter: ${error.message}`, 'error')
    }
  }
}

async function syncData(filter) {
  syncing.value = filter.id
  try {
    if (filter.type === 'feature') {
      await jiraService.syncJiraFeatures(props.releaseId)
      showSnackbar('Features synced successfully')
    } else {
      await jiraService.syncJiraCCBs(props.releaseId)
      showSnackbar('CCBs synced successfully')
    }
  } catch (error) {
    showSnackbar(`Sync failed: ${error.message}`, 'error')
  } finally {
    syncing.value = null
  }
}

function showSnackbar(message, color = 'success') {
  snackbar.value = {
    show: true,
    message,
    color,
  }
}
</script>

<style scoped>
.jira-settings {
  max-width: 1200px;
  margin: 0 auto;
}

.v-card-title {
  font-weight: 500;
}
</style>
