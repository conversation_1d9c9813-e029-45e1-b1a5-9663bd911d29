<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <div>
        <div class="d-flex align-center mb-2">
          <v-btn-toggle
            v-model="dataSource"
            color="primary"
            variant="outlined"
            mandatory
            class="mr-4"
          >
            <v-btn value="local" size="small">
              <v-icon>mdi-database</v-icon>
              Local Data
            </v-btn>
            <v-btn value="jira" size="small">
              <v-icon>mdi-jira</v-icon>
              JIRA Data
            </v-btn>
          </v-btn-toggle>

          <!-- JIRA Filter Input -->
          <v-text-field
            v-if="dataSource === 'jira'"
            v-model="jiraFilter"
            label="JIRA Filter"
            placeholder="project = PROJ AND status = 'In Progress'"
            variant="outlined"
            density="compact"
            style="width: 300px"
            class="mr-2"
            @keyup.enter="loadJiraFeatures"
          >
            <template #append-inner>
              <v-btn
                icon="mdi-magnify"
                size="small"
                variant="text"
                @click="loadJiraFeatures"
                :loading="syncing"
              />
            </template>
          </v-text-field>

          <v-btn
            v-if="dataSource === 'jira'"
            color="primary"
            variant="outlined"
            size="small"
            @click="syncJiraData"
            :loading="syncing"
            class="mr-2"
          >
            <v-icon>mdi-sync</v-icon>
            Sync
          </v-btn>
        </div>

        <div class="mt-2">
          <v-chip-group>
            <v-chip
              v-for="stat in getFeatureStats()"
              :key="stat.state"
              :color="stat.color"
              size="small"
              variant="flat"
              class="text-white mr-2"
              :style="{ backgroundColor: getChipBackgroundColor(stat.color) }"
            >
              {{ stat.state }}: {{ stat.count }}
            </v-chip>
          </v-chip-group>
        </div>
      </div>
      <v-btn color="primary" @click="showCreateDialog = true" :disabled="dataSource === 'jira'">
        <v-icon>mdi-plus</v-icon>
        Add Feature
      </v-btn>
    </div>

    <!-- Error Alert -->
    <v-alert
      v-if="showError"
      type="error"
      dismissible
      @click:close="showError = false"
      class="mb-4"
    >
      {{ errorMessage }}
    </v-alert>

    <v-data-table :headers="headers" :items="currentFeatures" class="grouped-table elevation-1">
      <template #item.description="{ item }">
        <div
          style="max-width: 200px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
        >
          {{ item.description || '-' }}
        </div>
      </template>

      <template #item.started="{ item }">
        {{ item.started ? new Date(item.started).toLocaleDateString() : '-' }}
      </template>

      <template #item.dev_done="{ item }">
        {{ item.dev_done ? new Date(item.dev_done).toLocaleDateString() : '-' }}
      </template>

      <template #item.ended="{ item }">
        {{ item.ended ? new Date(item.ended).toLocaleDateString() : '-' }}
      </template>

      <template #item.design_spec="{ item }">
        <a
          v-if="item.design_spec"
          :href="item.design_spec"
          target="_blank"
          class="text-decoration-none"
        >
          {{ item.design_spec }}
        </a>
        <span v-else>-</span>
      </template>

      <template #item.test_strategy="{ item }">
        <a
          v-if="item.test_strategy"
          :href="item.test_strategy"
          target="_blank"
          class="text-decoration-none"
        >
          {{ item.test_strategy }}
        </a>
        <span v-else>-</span>
      </template>

      <template #item.test_report="{ item }">
        <a
          v-if="item.test_report"
          :href="item.test_report"
          target="_blank"
          class="text-decoration-none"
        >
          {{ item.test_report }}
        </a>
        <span v-else>-</span>
      </template>

      <template #item.state="{ item }">
        <v-chip :color="getStateColor(item.state)" size="small" variant="flat" class="text-white">
          {{ item.state || '-' }}
        </v-chip>
      </template>

      <template #item.jira_key="{ item }">
        <a
          v-if="item.jira_key"
          :href="`${jiraBaseUrl}/browse/${item.jira_key}`"
          target="_blank"
          class="text-decoration-none"
        >
          {{ item.jira_key }}
        </a>
        <span v-else>-</span>
      </template>

      <template #item.is_from_jira="{ item }">
        <v-chip
          :color="item.is_from_jira ? 'blue' : 'grey'"
          size="small"
          variant="flat"
          class="text-white"
        >
          {{ item.is_from_jira ? 'JIRA' : 'Local' }}
        </v-chip>
      </template>

      <template #item.actions="{ item }">
        <v-btn
          icon
          size="small"
          variant="text"
          @click.stop="editFeature(item)"
          :disabled="item.is_from_jira"
        >
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn
          icon
          size="small"
          variant="text"
          color="error"
          @click.stop="deleteFeature(item)"
          :disabled="item.is_from_jira"
        >
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- Feature Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="800">
      <v-card>
        <v-card-title>{{ editingFeature?.id ? 'Edit Feature' : 'Add Feature' }}</v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.name" label="Name" required></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.owner" label="Owner"></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="formData.description"
                  label="Description"
                  rows="3"
                ></v-textarea>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-select v-model="formData.state" label="State" :items="stateOptions"></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.started"
                  label="Started Date"
                  type="date"
                  hint="When development started"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.dev_done"
                  label="Dev Done Date"
                  type="date"
                  hint="When development completed"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.ended"
                  label="End Date"
                  type="date"
                  hint="When feature was fully completed"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.design_spec" label="Design Spec URL"></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.test_strategy"
                  label="Test Strategy URL"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-text-field v-model="formData.test_report" label="Test Report URL"></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="closeDialog">Cancel</v-btn>
          <v-btn color="primary" @click="saveFeature">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Confirm Delete Dialog -->
    <v-dialog v-model="deleteDialogOpen" max-width="400px">
      <v-card>
        <v-card-title class="text-h5">Confirm Delete</v-card-title>
        <v-card-text>Are you sure you want to delete this feature?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialogOpen = false">Cancel</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleteLoading">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineProps, onMounted } from 'vue'
import { useReleaseStore } from '@/stores/release'
import { jiraService } from '@/services/jiraService'

const props = defineProps({
  releaseId: Number,
  features: Array,
})

const releaseStore = useReleaseStore()
const dataSource = ref('local')
const jiraFeatures = ref([])
const syncing = ref(false)
const jiraBaseUrl = ref('https://your-company.atlassian.net')
const errorMessage = ref('')
const showError = ref(false)
const jiraFilter = ref('')

// Computed property to get the current features based on data source
const currentFeatures = computed(() => {
  return dataSource.value === 'jira' ? jiraFeatures.value : props.features
})

// Watch for data source changes to load JIRA data when needed
watch(dataSource, async (newSource) => {
  if (newSource === 'jira' && jiraFeatures.value.length === 0) {
    await loadJiraFeatures()
  }
})

async function loadJiraFeatures() {
  try {
    syncing.value = true
    showError.value = false
    jiraFeatures.value = await jiraService.getFeaturesWithJira(props.releaseId, jiraFilter.value)
  } catch (error) {
    console.error('Failed to load JIRA features:', error)
    errorMessage.value = 'Failed to load JIRA features. Please check your JIRA configuration.'
    showError.value = true
    // Fall back to local data
    dataSource.value = 'local'
  } finally {
    syncing.value = false
  }
}

async function syncJiraData() {
  try {
    syncing.value = true
    showError.value = false
    await jiraService.syncJiraFeatures(props.releaseId, jiraFilter.value)
    await loadJiraFeatures()
  } catch (error) {
    console.error('Failed to sync JIRA features:', error)
    errorMessage.value =
      'Failed to sync JIRA features. Please check your JIRA configuration and try again.'
    showError.value = true
  } finally {
    syncing.value = false
  }
}

// Load JIRA base URL on component mount
async function loadJiraBaseUrl() {
  try {
    jiraBaseUrl.value = await jiraService.getJiraBaseUrl()
  } catch (error) {
    console.error('Failed to load JIRA base URL:', error)
  }
}
const showCreateDialog = ref(false)
const editingFeature = ref(null)
const formData = ref({
  name: '',
  description: '',
  owner: '',
  state: '',
  started: '',
  dev_done: '',
  ended: '',
  design_spec: '',
  test_strategy: '',
  test_report: '',
})

// Dynamic headers based on data source
const headers = computed(() => {
  const baseHeaders = [
    { title: 'Name', key: 'name' },
    { title: 'Description', key: 'description' },
    { title: 'Owner', key: 'owner' },
    { title: 'Started', key: 'started' },
    { title: 'Dev Done', key: 'dev_done' },
    { title: 'Ended', key: 'ended' },
    { title: 'Design Spec', key: 'design_spec' },
    { title: 'Test Strategy', key: 'test_strategy' },
    { title: 'Test Report', key: 'test_report' },
    { title: 'State', key: 'state' },
  ]

  if (dataSource.value === 'jira') {
    // Add JIRA-specific columns
    baseHeaders.splice(1, 0, { title: 'JIRA Key', key: 'jira_key' })
    baseHeaders.push({ title: 'Source', key: 'is_from_jira' })
  }

  baseHeaders.push({ title: 'Actions', key: 'actions', sortable: false })
  return baseHeaders
})

// Simplified JIRA-style state options
const stateOptions = ['BACKLOG', 'NEW', 'IN-PROGRESS', 'CANCELLED', 'CLOSED', 'RESOLVED']

// Get state color based on simplified JIRA conventions
function getStateColor(state) {
  const stateColors = {
    BACKLOG: 'grey',
    NEW: 'blue',
    'IN-PROGRESS': 'orange',
    CANCELLED: 'red',
    CLOSED: 'green',
    RESOLVED: 'teal',
  }
  return stateColors[state] || 'grey'
}

// Get explicit background color for chips to ensure consistency
function getChipBackgroundColor(color) {
  const colorMap = {
    grey: '#9E9E9E',
    blue: '#2196F3',
    orange: '#FF9800',
    red: '#F44336',
    green: '#4CAF50',
    teal: '#009688',
  }
  return colorMap[color] || '#9E9E9E'
}

// Get feature statistics by state
function getFeatureStats() {
  const stats = {}
  stateOptions.forEach((state) => {
    stats[state] = 0
  })

  currentFeatures.value?.forEach((feature) => {
    if (feature.state && Object.prototype.hasOwnProperty.call(stats, feature.state)) {
      stats[feature.state]++
    }
  })

  return stateOptions
    .map((state) => ({
      state,
      count: stats[state],
      color: getStateColor(state),
    }))
    .filter((stat) => stat.count > 0) // Only show states that have features
}

function editFeature(feature) {
  editingFeature.value = feature
  formData.value = {
    ...feature,
    started: feature.started ? new Date(feature.started).toISOString().split('T')[0] : '',
    dev_done: feature.dev_done ? new Date(feature.dev_done).toISOString().split('T')[0] : '',
    ended: feature.ended ? new Date(feature.ended).toISOString().split('T')[0] : '',
  }
  showCreateDialog.value = true
}

function deleteFeature(feature) {
  editingFeature.value = feature
  deleteDialogOpen.value = true
}

function resetForm() {
  formData.value = {
    name: '',
    description: '',
    owner: '',
    state: '',
    started: '',
    dev_done: '',
    ended: '',
    design_spec: '',
    test_strategy: '',
    test_report: '',
  }
}

function closeDialog() {
  showCreateDialog.value = false
  editingFeature.value = null
  resetForm()
}

async function saveFeature() {
  if (editingFeature.value?.id) {
    await releaseStore.updateFeature(editingFeature.value.id, formData.value)
  } else {
    await releaseStore.createFeature(props.releaseId, formData.value)
  }
  closeDialog()
}

const deleteDialogOpen = ref(false)
const deleteLoading = ref(false)

async function confirmDelete() {
  deleteLoading.value = true
  try {
    await releaseStore.deleteFeature(editingFeature.value.id)
    deleteDialogOpen.value = false
  } catch (error) {
    console.error('Error deleting feature:', error)
  } finally {
    deleteLoading.value = false
  }
}

// Load JIRA base URL on component mount
onMounted(() => {
  loadJiraBaseUrl()
})
</script>

<style scoped>
/* 统一表格样式 */
.grouped-table :deep(.v-data-table__th) {
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
  color: #495057 !important;
}

.grouped-table :deep(.v-data-table__td) {
  padding: 8px 12px !important;
}

.v-data-table tbody tr {
  cursor: pointer;
}

.v-data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
