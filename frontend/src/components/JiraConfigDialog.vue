<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="600"
  >
    <v-card>
      <v-card-title>
        {{ config?.id ? 'Edit JIRA Configuration' : 'Create JIRA Configuration' }}
      </v-card-title>

      <v-card-text>
        <v-form ref="form" v-model="valid">
          <v-text-field
            v-model="formData.name"
            label="Configuration Name"
            :rules="[(v) => !!v || 'Name is required']"
            required
            variant="outlined"
            class="mb-3"
          ></v-text-field>

          <v-text-field
            v-model="formData.base_url"
            label="JIRA Base URL"
            :rules="[
              (v) => !!v || 'Base URL is required',
              (v) => isValidUrl(v) || 'Please enter a valid URL'
            ]"
            placeholder="https://your-company.atlassian.net"
            required
            variant="outlined"
            class="mb-3"
          ></v-text-field>

          <v-text-field
            v-model="formData.username"
            label="Username/Email"
            :rules="[(v) => !!v || 'Username is required']"
            required
            variant="outlined"
            class="mb-3"
          ></v-text-field>

          <v-text-field
            v-model="formData.api_token"
            label="API Token"
            :rules="[(v) => !!v || 'API Token is required']"
            :type="showPassword ? 'text' : 'password'"
            :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append-inner="showPassword = !showPassword"
            required
            variant="outlined"
            class="mb-3"
            hint="Generate an API token from your JIRA account settings"
            persistent-hint
          ></v-text-field>

          <v-switch
            v-model="formData.is_active"
            label="Set as Active Configuration"
            color="primary"
            class="mb-3"
          ></v-switch>

          <v-alert
            v-if="testResult"
            :type="testResult.success ? 'success' : 'error'"
            class="mb-3"
          >
            {{ testResult.message }}
          </v-alert>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-btn
          color="primary"
          variant="outlined"
          @click="testConnection"
          :loading="testLoading"
          :disabled="!canTest"
        >
          <v-icon>mdi-connection</v-icon>
          Test Connection
        </v-btn>
        
        <v-spacer></v-spacer>
        
        <v-btn
          color="grey"
          variant="text"
          @click="$emit('update:modelValue', false)"
        >
          Cancel
        </v-btn>
        
        <v-btn
          color="primary"
          variant="flat"
          @click="save"
          :loading="loading"
          :disabled="!valid"
        >
          {{ config?.id ? 'Update' : 'Create' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed, defineProps, defineEmits } from 'vue'
import { jiraService } from '@/services/jiraService'

const props = defineProps({
  modelValue: Boolean,
  config: Object,
  loading: Boolean,
})

const emit = defineEmits(['update:modelValue', 'save'])

const form = ref(null)
const valid = ref(false)
const showPassword = ref(false)
const testLoading = ref(false)
const testResult = ref(null)

const formData = ref({
  name: '',
  base_url: '',
  username: '',
  api_token: '',
  is_active: false,
})

const canTest = computed(() => {
  return formData.value.base_url && 
         formData.value.username && 
         formData.value.api_token
})

watch(
  () => props.config,
  (newConfig) => {
    if (newConfig) {
      formData.value = {
        name: newConfig.name || '',
        base_url: newConfig.base_url || '',
        username: newConfig.username || '',
        api_token: newConfig.api_token === '***' ? '' : newConfig.api_token || '',
        is_active: newConfig.is_active || false,
      }
    } else {
      resetForm()
    }
    testResult.value = null
  },
  { immediate: true }
)

function resetForm() {
  formData.value = {
    name: '',
    base_url: '',
    username: '',
    api_token: '',
    is_active: false,
  }
  testResult.value = null
}

function isValidUrl(url) {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

async function testConnection() {
  if (!canTest.value) return
  
  testLoading.value = true
  testResult.value = null
  
  try {
    // Create a temporary config for testing
    const tempConfig = await jiraService.createJiraConfig({
      ...formData.value,
      name: `temp_test_${Date.now()}`,
      is_active: false,
    })
    
    try {
      await jiraService.testJiraConnection(tempConfig.id)
      testResult.value = {
        success: true,
        message: 'Connection successful! JIRA configuration is valid.',
      }
    } catch (error) {
      testResult.value = {
        success: false,
        message: `Connection failed: ${error.message}`,
      }
    } finally {
      // Clean up temporary config
      await jiraService.deleteJiraConfig(tempConfig.id)
    }
  } catch (error) {
    testResult.value = {
      success: false,
      message: `Test failed: ${error.message}`,
    }
  } finally {
    testLoading.value = false
  }
}

async function save() {
  const isValid = await form.value?.validate()
  if (isValid?.valid) {
    const data = { ...formData.value }
    
    // Don't send empty API token if it's masked
    if (props.config?.id && data.api_token === '') {
      data.api_token = '***'
    }
    
    emit('save', data)
  }
}
</script>

<style scoped>
.v-card-title {
  font-weight: 500;
}
</style>
