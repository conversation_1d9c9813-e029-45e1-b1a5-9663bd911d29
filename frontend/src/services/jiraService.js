// JIRA Service - Simplified version using environment-based backend configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://192.168.102.208:8888'

// Get JIRA base URL for link generation
export async function getJiraBaseUrl() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/jira/base-url`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data.base_url
  } catch (error) {
    console.error('Error getting JIRA base URL:', error)
    // Fallback to placeholder
    return 'https://your-company.atlassian.net'
  }
}

// Test JIRA connection
export async function testJiraConnection() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/jira/test-connection`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error testing JIRA connection:', error)
    throw error
  }
}

// Get features with JIRA integration
export async function getFeaturesWithJira(releaseId, filter = '') {
  try {
    const url = new URL(`${API_BASE_URL}/api/releases/${releaseId}/features/jira`)
    if (filter) {
      url.searchParams.append('filter', filter)
    }
    
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return await response.json()
  } catch (error) {
    console.error('Error getting features with JIRA:', error)
    throw error
  }
}

// Get CCBs with JIRA integration
export async function getCCBsWithJira(releaseId, filter = '') {
  try {
    const url = new URL(`${API_BASE_URL}/api/releases/${releaseId}/ccbs/jira`)
    if (filter) {
      url.searchParams.append('filter', filter)
    }
    
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return await response.json()
  } catch (error) {
    console.error('Error getting CCBs with JIRA:', error)
    throw error
  }
}

// Sync JIRA features (same as getFeaturesWithJira but with explicit sync intent)
export async function syncJiraFeatures(releaseId, filter = '') {
  return getFeaturesWithJira(releaseId, filter)
}

// Sync JIRA CCBs (same as getCCBsWithJira but with explicit sync intent)
export async function syncJiraCCBs(releaseId, filter = '') {
  return getCCBsWithJira(releaseId, filter)
}

// Export as default object for easier importing
export const jiraService = {
  getJiraBaseUrl,
  testJiraConnection,
  getFeaturesWithJira,
  getCCBsWithJira,
  syncJiraFeatures,
  syncJiraCCBs
}

export default jiraService
