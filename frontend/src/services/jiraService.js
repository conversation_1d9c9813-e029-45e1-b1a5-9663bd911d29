// 简化的JIRA服务 - 后端固定配置，前端只负责数据获取和过滤器设置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://192.168.102.208:8888'

export const jiraService = {
  // JIRA数据同步和获取 - 支持过滤器参数
  async getFeaturesWithJira(releaseId, filterQuery = '') {
    const url = filterQuery 
      ? `${API_BASE_URL}/api/releases/${releaseId}/features/jira?filter=${encodeURIComponent(filterQuery)}`
      : `${API_BASE_URL}/api/releases/${releaseId}/features/jira`
    
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Failed to fetch features with JIRA data')
    }
    return response.json()
  },

  async getCCBsWithJira(releaseId, filterQuery = '') {
    const url = filterQuery 
      ? `${API_BASE_URL}/api/releases/${releaseId}/ccbs/jira?filter=${encodeURIComponent(filterQuery)}`
      : `${API_BASE_URL}/api/releases/${releaseId}/ccbs/jira`
    
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Failed to fetch CCBs with JIRA data')
    }
    return response.json()
  },

  async syncJiraFeatures(releaseId, filterQuery = '') {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/features/sync-jira`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filter: filterQuery }),
    })
    if (!response.ok) {
      throw new Error('Failed to sync JIRA features')
    }
    return response.json()
  },

  async syncJiraCCBs(releaseId, filterQuery = '') {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/ccbs/sync-jira`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filter: filterQuery }),
    })
    if (!response.ok) {
      throw new Error('Failed to sync JIRA CCBs')
    }
    return response.json()
  },

  // 获取JIRA基础URL用于链接 - 从后端获取固定配置
  async getJiraBaseUrl() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/jira/base-url`)
      if (response.ok) {
        const data = await response.json()
        return data.base_url || 'https://your-company.atlassian.net'
      }
    } catch (error) {
      console.error('Failed to get JIRA base URL:', error)
    }
    return 'https://your-company.atlassian.net'
  },
}
