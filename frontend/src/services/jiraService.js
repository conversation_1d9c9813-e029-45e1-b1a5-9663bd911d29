// JIRA配置相关API服务
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://192.168.102.208:8888'

export const jiraService = {
  // JIRA连接配置
  async getJiraConfigs() {
    const response = await fetch(`${API_BASE_URL}/api/jira/configs`)
    if (!response.ok) {
      throw new Error('Failed to fetch JIRA configs')
    }
    return response.json()
  },

  async createJiraConfig(config) {
    const response = await fetch(`${API_BASE_URL}/api/jira/configs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    })
    if (!response.ok) {
      throw new Error('Failed to create JIRA config')
    }
    return response.json()
  },

  async updateJiraConfig(id, config) {
    const response = await fetch(`${API_BASE_URL}/api/jira/configs/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    })
    if (!response.ok) {
      throw new Error('Failed to update JIRA config')
    }
    return response.json()
  },

  async deleteJiraConfig(id) {
    const response = await fetch(`${API_BASE_URL}/api/jira/configs/${id}`, {
      method: 'DELETE',
    })
    if (!response.ok) {
      throw new Error('Failed to delete JIRA config')
    }
    return response.json()
  },

  async testJiraConnection(id) {
    const response = await fetch(`${API_BASE_URL}/api/jira/configs/${id}/test`, {
      method: 'POST',
    })
    if (!response.ok) {
      throw new Error('JIRA connection test failed')
    }
    return response.json()
  },

  // JIRA过滤器配置
  async getFilterConfigs(releaseId) {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/jira-filters`)
    if (!response.ok) {
      throw new Error('Failed to fetch filter configs')
    }
    return response.json()
  },

  async createFilterConfig(releaseId, config) {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/jira-filters`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    })
    if (!response.ok) {
      throw new Error('Failed to create filter config')
    }
    return response.json()
  },

  async updateFilterConfig(id, config) {
    const response = await fetch(`${API_BASE_URL}/api/jira/filters/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    })
    if (!response.ok) {
      throw new Error('Failed to update filter config')
    }
    return response.json()
  },

  async deleteFilterConfig(id) {
    const response = await fetch(`${API_BASE_URL}/api/jira/filters/${id}`, {
      method: 'DELETE',
    })
    if (!response.ok) {
      throw new Error('Failed to delete filter config')
    }
    return response.json()
  },

  // JIRA字段映射
  async getFieldMappings(type = '') {
    const url = type 
      ? `${API_BASE_URL}/api/jira/field-mappings?type=${type}`
      : `${API_BASE_URL}/api/jira/field-mappings`
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Failed to fetch field mappings')
    }
    return response.json()
  },

  async createFieldMapping(mapping) {
    const response = await fetch(`${API_BASE_URL}/api/jira/field-mappings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mapping),
    })
    if (!response.ok) {
      throw new Error('Failed to create field mapping')
    }
    return response.json()
  },

  async updateFieldMapping(id, mapping) {
    const response = await fetch(`${API_BASE_URL}/api/jira/field-mappings/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mapping),
    })
    if (!response.ok) {
      throw new Error('Failed to update field mapping')
    }
    return response.json()
  },

  async deleteFieldMapping(id) {
    const response = await fetch(`${API_BASE_URL}/api/jira/field-mappings/${id}`, {
      method: 'DELETE',
    })
    if (!response.ok) {
      throw new Error('Failed to delete field mapping')
    }
    return response.json()
  },

  // 获取带JIRA数据的Features和CCBs
  async getFeaturesWithJira(releaseId) {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/features-with-jira`)
    if (!response.ok) {
      throw new Error('Failed to fetch features with JIRA data')
    }
    return response.json()
  },

  async getCCBsWithJira(releaseId) {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/ccbs-with-jira`)
    if (!response.ok) {
      throw new Error('Failed to fetch CCBs with JIRA data')
    }
    return response.json()
  },

  // 同步JIRA数据
  async syncJiraFeatures(releaseId) {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/sync-jira-features`, {
      method: 'POST',
    })
    if (!response.ok) {
      throw new Error('Failed to sync JIRA features')
    }
    return response.json()
  },

  async syncJiraCCBs(releaseId) {
    const response = await fetch(`${API_BASE_URL}/api/releases/${releaseId}/sync-jira-ccbs`, {
      method: 'POST',
    })
    if (!response.ok) {
      throw new Error('Failed to sync JIRA CCBs')
    }
    return response.json()
  },
}

export default jiraService
