/* Global styles */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
}

.text-primary {
  color: #1976D2 !important;
}

.text-grey {
  color: #757575 !important;
}

/* Custom utility classes */
.pa-0 {
  padding: 0 !important;
}

.ma-0 {
  margin: 0 !important;
}

/* Global Table Styles - 全局表格样式 */
/* 为所有表头添加优雅的突出效果 */

/* 表头样式 - 优雅的浅灰色背景 */
.v-data-table .v-data-table__thead {
  background-color: #f8f9fa !important;
}

.v-data-table .v-data-table__thead .v-data-table__tr {
  background-color: #f8f9fa !important;
}

.v-data-table .v-data-table__thead .v-data-table__th {
  background-color: #f8f9fa !important;
  color: #495057 !important;
  font-weight: 600 !important;
  border-bottom: 1px solid #dee2e6 !important;
  font-size: 0.875rem !important;
  padding: 12px 16px !important;
}

/* 确保表头文字和图标可见性 */
.v-data-table .v-data-table__thead .v-data-table__th .v-data-table-header__content {
  color: #495057 !important;
  font-weight: 600 !important;
}

.v-data-table .v-data-table__thead .v-data-table__th .v-data-table-header__content .v-icon {
  color: #6c757d !important;
}

/* 排序图标样式 */
.v-data-table .v-data-table__thead .v-data-table__th .v-data-table-header__sort-icon {
  color: #6c757d !important;
}

/* 表头悬停效果 */
.v-data-table .v-data-table__thead .v-data-table__th:hover {
  background-color: #e9ecef !important;
}