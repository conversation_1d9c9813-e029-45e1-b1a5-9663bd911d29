# JIRA后端配置指南

## 概述

根据新的架构设计，JIRA的URL和认证信息现在固定配置在后端，前端不再需要配置界面。

## 需要实现的后端更改

### 1. 固定JIRA配置

在后端代码中硬编码JIRA配置信息：

```go
// config/jira.go 或在现有配置文件中
type JiraConfig struct {
    BaseURL  string
    Username string
    Token    string
}

func GetJiraConfig() *JiraConfig {
    return &JiraConfig{
        BaseURL:  "https://your-company.atlassian.net",
        Username: "<EMAIL>",
        Token:    "your-jira-api-token",
    }
}
```

### 2. 新增API端点

需要添加以下端点：

#### GET /api/jira/base-url
返回JIRA基础URL用于前端生成链接：

```go
func GetJiraBaseURL(c *gin.Context) {
    config := GetJiraConfig()
    c.JSON(http.StatusOK, gin.H{
        "base_url": config.BaseURL,
    })
}
```

#### 更新现有端点支持过滤器参数

修改以下端点以支持filter查询参数：

- `GET /api/releases/{id}/features/jira?filter={jql_or_filter_id}`
- `GET /api/releases/{id}/ccbs/jira?filter={jql_or_filter_id}`
- `POST /api/releases/{id}/features/sync-jira` (body中包含filter)
- `POST /api/releases/{id}/ccbs/sync-jira` (body中包含filter)

### 3. 移除配置相关代码

可以删除以下数据库表和相关代码：
- JIRA配置表
- JIRA过滤器配置表
- JIRA字段映射表（如果不需要自定义映射）

### 4. 环境变量配置（推荐）

为了安全性，建议使用环境变量：

```go
func GetJiraConfig() *JiraConfig {
    return &JiraConfig{
        BaseURL:  os.Getenv("JIRA_BASE_URL"),
        Username: os.Getenv("JIRA_USERNAME"), 
        Token:    os.Getenv("JIRA_API_TOKEN"),
    }
}
```

对应的环境变量：
```bash
JIRA_BASE_URL=https://your-company.atlassian.net
JIRA_USERNAME=<EMAIL>
JIRA_API_TOKEN=your-jira-api-token
```

## 前端已完成的更改

✅ 移除了所有JIRA配置组件和页面
✅ 简化了jiraService.js，移除配置管理
✅ 在Feature和CCB页面添加了过滤器输入
✅ 修改了编辑对话框，使JIRA字段只读
✅ 移除了数据源切换，所有数据来自JIRA

## 测试建议

1. 配置后端JIRA连接信息
2. 测试过滤器功能（JQL查询和Filter ID）
3. 测试数据同步功能
4. 验证编辑功能（JIRA字段只读，本地字段可编辑）
5. 确认JIRA链接正常工作

## 安全注意事项

- 不要将JIRA认证信息提交到版本控制
- 使用环境变量或安全的配置管理系统
- 定期轮换API Token
- 限制JIRA用户权限为只读（如果只需要读取数据）
