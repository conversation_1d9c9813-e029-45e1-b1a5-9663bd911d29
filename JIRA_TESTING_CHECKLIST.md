# JIRA Integration Testing Checklist

## Pre-Testing Setup

### 1. Backend Services
- [ ] Ensure PostgreSQL database is running
- [ ] Verify backend server is running on correct port
- [ ] Check that JIRA-related database tables exist (run migrations if needed)
- [ ] Test backend API endpoints manually (optional)

### 2. Frontend Environment
- [ ] Ensure frontend development server is running
- [ ] Check that environment variables are set correctly
- [ ] Verify no console errors on page load

## JIRA Configuration Testing

### 3. JIRA Settings Access
- [ ] Navigate to Dashboard
- [ ] Click on "JIRA Settings" tab
- [ ] Verify JiraSettings component loads without errors
- [ ] Check that all UI elements are visible and styled correctly

### 4. JIRA Configuration Management
- [ ] Click "Add Configuration" button
- [ ] Fill in JIRA configuration form:
  - [ ] Base URL (e.g., https://your-company.atlassian.net)
  - [ ] Username
  - [ ] Password/API Token
  - [ ] Configuration name
- [ ] Test "Test Connection" button (may fail without real JIRA)
- [ ] Save configuration
- [ ] Verify configuration appears in the list
- [ ] Test edit and delete functionality

### 5. Filter Configuration
- [ ] Click "Add Filter" button in JIRA Settings
- [ ] Configure filter settings:
  - [ ] Select filter type (Feature/CCB)
  - [ ] Choose method (Filter ID or JQL)
  - [ ] Enter filter details
- [ ] Save filter configuration
- [ ] Verify filter appears in the list

## Feature Summary JIRA Integration

### 6. Data Source Toggle
- [ ] Navigate to Feature Summary tab
- [ ] Verify "Local" and "JIRA" toggle buttons are visible
- [ ] Click "JIRA" button
- [ ] Verify loading state appears
- [ ] Check that headers change to include JIRA-specific columns:
  - [ ] "JIRA Key" column appears
  - [ ] "Source" column appears

### 7. JIRA Data Display
- [ ] Verify JIRA data loads (or shows appropriate error)
- [ ] Check that JIRA Key column shows clickable links
- [ ] Verify Source column shows "JIRA" for JIRA items
- [ ] Test clicking JIRA key links (should open JIRA in new tab)
- [ ] Verify edit/delete buttons are disabled for JIRA items

### 8. Error Handling
- [ ] Test with invalid JIRA configuration
- [ ] Verify error alert appears with helpful message
- [ ] Check that system falls back to local data
- [ ] Test dismissing error alerts

### 9. Sync Functionality
- [ ] Click "Sync JIRA Data" button
- [ ] Verify loading state during sync
- [ ] Check that data refreshes after sync
- [ ] Test sync with no JIRA configuration

## CCB Summary JIRA Integration

### 10. CCB Data Source Toggle
- [ ] Navigate to CCB Summary tab
- [ ] Verify "Local" and "JIRA" toggle buttons are visible
- [ ] Click "JIRA" button
- [ ] Verify loading state appears
- [ ] Check that headers change to include JIRA-specific columns

### 11. CCB JIRA Data Display
- [ ] Verify JIRA data loads (or shows appropriate error)
- [ ] Check that JIRA Key column shows clickable links
- [ ] Verify Source column shows "JIRA" for JIRA items
- [ ] Test clicking JIRA key links
- [ ] Verify edit/delete buttons are disabled for JIRA items

### 12. CCB Error Handling
- [ ] Test with invalid JIRA configuration
- [ ] Verify error alert appears
- [ ] Check fallback to local data
- [ ] Test error alert dismissal

## Mixed Data Scenarios

### 13. Local + JIRA Data
- [ ] Create some local features/CCBs
- [ ] Switch to JIRA data source
- [ ] Switch back to local data source
- [ ] Verify data persists correctly
- [ ] Check that statistics update correctly for each source

### 14. Data Source Persistence
- [ ] Set data source to JIRA
- [ ] Refresh the page
- [ ] Verify data source resets to local (expected behavior)
- [ ] Test navigation between tabs maintains state

## UI/UX Testing

### 15. Visual Design
- [ ] Verify all buttons have consistent styling
- [ ] Check that loading states are visually clear
- [ ] Ensure error messages are prominently displayed
- [ ] Verify JIRA-specific columns align properly
- [ ] Check responsive design on different screen sizes

### 16. User Experience
- [ ] Test workflow: Settings → Configuration → Filter → Data Toggle
- [ ] Verify intuitive navigation between components
- [ ] Check that disabled states are clearly indicated
- [ ] Test keyboard navigation and accessibility

## Performance Testing

### 17. Loading Performance
- [ ] Measure time to load JIRA data
- [ ] Test with large datasets (if available)
- [ ] Verify no memory leaks during data source switching
- [ ] Check network requests are optimized

## Error Scenarios

### 18. Network Issues
- [ ] Test with network disconnected
- [ ] Verify graceful error handling
- [ ] Test with slow network connections
- [ ] Check timeout handling

### 19. Invalid Configurations
- [ ] Test with invalid JIRA URLs
- [ ] Test with wrong credentials
- [ ] Test with non-existent filters
- [ ] Verify all scenarios show appropriate errors

## Browser Compatibility

### 20. Cross-Browser Testing
- [ ] Test in Chrome
- [ ] Test in Firefox
- [ ] Test in Safari (if available)
- [ ] Test in Edge
- [ ] Verify consistent behavior across browsers

## Final Validation

### 21. Complete User Journey
- [ ] Start from fresh state (no configurations)
- [ ] Complete full setup: Config → Filter → Usage
- [ ] Test switching between multiple releases
- [ ] Verify data isolation between releases
- [ ] Test complete workflow end-to-end

### 22. Code Quality
- [ ] Check browser console for errors
- [ ] Verify no TypeScript/linting errors
- [ ] Test component cleanup (no memory leaks)
- [ ] Verify proper error boundaries

## Notes for Production Deployment

### Required for Live JIRA Integration:
1. **Real JIRA Instance**: Update placeholder URLs with actual JIRA base URLs
2. **Authentication**: Configure proper JIRA credentials and permissions
3. **API Permissions**: Ensure JIRA user has read access to required projects
4. **Network Access**: Verify backend can reach JIRA instance
5. **SSL Certificates**: Ensure proper HTTPS configuration for JIRA connections

### Optional Enhancements:
1. **Caching**: Implement Redis or similar for JIRA data caching
2. **Webhooks**: Set up JIRA webhooks for real-time updates
3. **Field Mapping**: Implement custom field mapping interface
4. **Bulk Operations**: Add bulk sync and management features
5. **Audit Logging**: Track JIRA integration usage and changes
