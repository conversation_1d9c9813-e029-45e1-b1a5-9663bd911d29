# Release Management System - Network Configuration

## 🌐 Network Access Setup

The system is now configured to be accessible from other machines on the network.

### Current Configuration

- **Frontend**: Runs on `http://***************:3000`
- **Backend API**: Runs on `http://***************:8080`
- **Database**: PostgreSQL on `localhost:5432` (internal)

### Access URLs

| Service | Local Access | Network Access |
|---------|-------------|----------------|
| Frontend | <http://localhost:3000> | <http://***************:3000> |
| Backend API | <http://localhost:8080/api> | <http://***************:8080/api> |

### 🔧 How to Change IP Address

#### Option 1: Quick Startup Script

```bash
# Edit the IP address in start-services.sh
export SERVER_IP="YOUR_IP_ADDRESS"
./start-services.sh
```

#### Option 2: Manual Configuration

1. **Backend Configuration** (`backend/main.go`):
   - Update CORS AllowOrigins to include your IP
   - Server automatically listens on all interfaces (0.0.0.0)

2. **Frontend Configuration** (`frontend/src/stores/release.js`):

   ```javascript
   const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://YOUR_IP:8080'
   ```

3. **Environment Variables**:

   ```bash
   # Backend
   export SERVER_HOST=0.0.0.0
   export PORT=8080
   
   # Frontend
   export VITE_API_BASE_URL=http://YOUR_IP:8080
   ```

### 🚀 Quick Start Commands

#### Start All Services

```bash
./start-services.sh
```

#### Start Services Manually

```bash
# Backend
cd backend
export DB_HOST=localhost DB_PORT=5432 DB_USER=postgres DB_PASSWORD=postgres DB_NAME=release_management
export SERVER_HOST=0.0.0.0 PORT=8080
go run main.go

# Frontend (in another terminal)
cd frontend
export VITE_API_BASE_URL=http://***************:8080
npm run dev
```

### 🔒 Security Notes

- CORS is configured to allow access from:
  - `http://localhost:3000`
  - `http://***************:3000`
  - `http://127.0.0.1:3000`
- Database is only accessible locally (localhost)
- Services listen on all network interfaces (0.0.0.0)

### 🐛 Troubleshooting

1. **Port Already in Use**:

   ```bash
   # Kill existing processes
   pkill -f "go run main.go"
   pkill -f "npm run dev"
   
   # Or use port killers
   lsof -ti:8080 | xargs kill -9
   lsof -ti:3000 | xargs kill -9
   ```

2. **API Not Accessible**:
   - Check firewall settings
   - Verify IP address is correct
   - Ensure services are running: `ps aux | grep -E "(go run|npm)"`

3. **CORS Errors**:
   - Add your IP to the CORS AllowOrigins in `backend/main.go`
   - Restart backend service

### 📝 Logs

- Backend logs: `backend.log`
- Frontend logs: `frontend.log`
- View logs: `tail -f backend.log frontend.log`
